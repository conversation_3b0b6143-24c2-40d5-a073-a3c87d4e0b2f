# دليل البدء السريع - إرسال الإشعارات المجدولة عبر Supabase

## ملخص سريع

هذا النظام يحول إرسال الإشعارات المجدولة من التطبيق إلى Supabase، بحيث تُرسل الإشعارات حتى لو كان التطبيق مغلقاً.

## الخطوات السريعة

### 1. إعداد Firebase (5 دقائق)

```bash
# 1. اذهب إلى Firebase Console
# 2. Project Settings > Service Accounts
# 3. Generate new private key
# 4. احفظ المعلومات التالية:
```

- `project_id`
- `client_email` 
- `private_key`

### 2. نشر Edge Function (3 دقائق)

```bash
# ربط المشروع
supabase link --project-ref YOUR_PROJECT_REF

# نشر Function
supabase functions deploy send-fcm-notifications

# إعداد المتغيرات
supabase secrets set FIREBASE_PROJECT_ID=your-project-id
supabase secrets set FIREBASE_CLIENT_EMAIL=your-client-email
supabase secrets set FIREBASE_PRIVATE_KEY="your-private-key"
```

### 3. تطبيق Database Changes (2 دقيقة)

```bash
# تطبيق التحديثات
supabase db push
```

### 4. إعداد Webhook (3 دقائق)

1. اذهب إلى Supabase Dashboard > Database > Webhooks
2. Create new hook:
   - **Name**: FCM Notification Sender
   - **Table**: notification_logs
   - **Events**: Insert
   - **Type**: Edge Function
   - **Function**: send-fcm-notifications

### 5. تفعيل Cron Job (1 دقيقة)

```sql
-- في SQL Editor
CREATE EXTENSION IF NOT EXISTS pg_cron;

SELECT cron.schedule(
    'process-notifications',
    '* * * * *',
    'SELECT process_scheduled_notifications_with_logging();'
);
```

## اختبار سريع

### اختبار النظام:

```sql
-- 1. تشغيل فحص الإشعارات يدوياً
SELECT * FROM send_notifications_with_fcm();

-- 2. فحص النتائج
SELECT 
    title,
    body,
    status,
    created_at
FROM notification_logs 
ORDER BY created_at DESC 
LIMIT 5;
```

### اختبار إشعار مباشر:

```sql
-- إدراج إشعار تجريبي (استبدل FCM_TOKEN بـ token حقيقي)
INSERT INTO notification_logs (
    patient_id,
    fcm_token,
    title,
    body,
    status
) VALUES (
    'test-patient',
    'YOUR_REAL_FCM_TOKEN',
    'اختبار الإشعار',
    'هذا إشعار تجريبي من Supabase',
    'pending'
);
```

## مراقبة النظام

### فحص الحالة:

```sql
-- حالة Cron Jobs
SELECT * FROM get_cron_job_status();

-- إحصائيات اليوم
SELECT * FROM notification_system_stats 
WHERE date = CURRENT_DATE;

-- آخر الإشعارات
SELECT 
    created_at,
    title,
    status,
    CASE 
        WHEN sent_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (sent_at - created_at))::INTEGER 
        ELSE NULL 
    END as processing_seconds
FROM notification_logs 
WHERE patient_id != 'system'
ORDER BY created_at DESC 
LIMIT 10;
```

## استكشاف الأخطاء السريع

### 1. الإشعارات لا تُرسل:

```sql
-- فحص الإشعارات المعلقة
SELECT COUNT(*) FROM notification_logs WHERE status = 'pending';

-- فحص آخر خطأ
SELECT error_message FROM notification_logs 
WHERE status = 'failed' 
ORDER BY created_at DESC 
LIMIT 1;
```

### 2. Cron Job لا يعمل:

```sql
-- فحص Cron Jobs
SELECT jobname, active FROM cron.job;

-- فحص آخر تشغيل
SELECT * FROM cron_job_logs 
ORDER BY started_at DESC 
LIMIT 3;
```

### 3. Edge Function تفشل:

- اذهب إلى Supabase Dashboard > Edge Functions > send-fcm-notifications > Logs
- فحص Environment Variables في Settings

### 4. Firebase Authentication:

```bash
# تأكد من صحة المتغيرات
supabase secrets list

# إعادة تعيين المتغيرات
supabase secrets set FIREBASE_PRIVATE_KEY="your-correct-key"
```

## نصائح مهمة

### 1. FCM Tokens:
- تأكد من تحديث tokens في التطبيق
- نظف tokens غير النشطة دورياً

### 2. المراقبة:
- راقب logs يومياً
- تحقق من معدل نجاح الإرسال

### 3. الأداء:
- النظام يدعم آلاف الإشعارات يومياً
- معدل المعالجة: ~1-3 ثوان لكل إشعار

### 4. التكلفة:
- Edge Functions: مجانية حتى 500,000 استدعاء شهرياً
- Database: حسب خطة Supabase
- Firebase FCM: مجاني

## الدعم

### Logs مفيدة:
- **Edge Function Logs**: Supabase Dashboard > Edge Functions
- **Database Logs**: جدول `cron_job_logs`
- **Notification Logs**: جدول `notification_logs`

### أوامر تشخيص:
```sql
-- معدل النجاح العام
SELECT 
    status,
    COUNT(*),
    ROUND(COUNT(*)::DECIMAL / (SELECT COUNT(*) FROM notification_logs WHERE patient_id != 'system') * 100, 2) as percentage
FROM notification_logs 
WHERE patient_id != 'system'
GROUP BY status;

-- أداء آخر 24 ساعة
SELECT 
    DATE_TRUNC('hour', created_at) as hour,
    COUNT(*) as notifications,
    COUNT(*) FILTER (WHERE status = 'delivered') as delivered
FROM notification_logs 
WHERE created_at > NOW() - INTERVAL '24 hours'
AND patient_id != 'system'
GROUP BY hour
ORDER BY hour DESC;
```

---

**🎉 تهانينا! النظام جاهز للعمل**

الآن الإشعارات ستُرسل تلقائياً من Supabase بدون الحاجة لأن يكون التطبيق مفتوحاً.
