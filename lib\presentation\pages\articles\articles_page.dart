import 'package:deit_rx/presentation/widgets/common/filter_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../domain/entities/article.dart';
import '../../../data/repositories/article_repository.dart';
import '../../widgets/common/custom_app_bar.dart';
import 'article_detail_page.dart';

class ArticlesPage extends StatefulWidget {
  const ArticlesPage({super.key});

  @override
  State<ArticlesPage> createState() => _ArticlesPageState();
}

class _ArticlesPageState extends State<ArticlesPage> {
  final ArticleRepository _articleRepository = ArticleRepository();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Article> _articles = [];

  List<Map<String, dynamic>> _categoriesWithCount = [];
  String? _selectedCategoryId;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasError = false;
  String _errorMessage = '';
  String _searchTerm = '';
  int _currentPage = 0;
  final int _pageSize = 10;
  bool _hasMoreData = true;
  bool _hasLoadedData = false;

  @override
  void initState() {
    super.initState();
    // لا نحمل البيانات عند التهيئة - سيتم التحميل عند الحاجة
    _scrollController.addListener(_onScroll);
  }

  void _loadDataIfNeeded() {
    if (!_hasLoadedData) {
      _loadInitialData();
      _hasLoadedData = true;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMoreData) {
        _loadMoreArticles();
      }
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final categoriesWithCount =
          await _articleRepository.getCategoriesWithCount();
      final articles = await _articleRepository.getAllArticles(
        categoryId: _selectedCategoryId,
        searchTerm: _searchTerm.isEmpty ? null : _searchTerm,
        limit: _pageSize,
        offset: 0,
      );

      if (mounted) {
        setState(() {
          _categoriesWithCount = categoriesWithCount;
          _articles = articles;
          _currentPage = 0;
          _hasMoreData = articles.length == _pageSize;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreArticles() async {
    if (_isLoadingMore || !_hasMoreData) return;

    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      final newArticles = await _articleRepository.getAllArticles(
        categoryId: _selectedCategoryId,
        searchTerm: _searchTerm.isEmpty ? null : _searchTerm,
        limit: _pageSize,
        offset: (_currentPage + 1) * _pageSize,
      );

      if (mounted) {
        setState(() {
          _articles.addAll(newArticles);
          _currentPage++;
          _hasMoreData = newArticles.length == _pageSize;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  Future<void> _refreshArticles() async {
    _currentPage = 0;
    _hasMoreData = true;
    await _loadInitialData();
  }

  void _onCategoryChanged(String? categoryId) {
    if (_selectedCategoryId != categoryId) {
      if (mounted) {
        setState(() {
          _selectedCategoryId = categoryId;
        });
      }
      _refreshArticles();
    }
  }

  void _onSearchChanged(String searchTerm) {
    if (_searchTerm != searchTerm) {
      if (mounted) {
        setState(() {
          _searchTerm = searchTerm;
        });
      }
      _refreshArticles();
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحميل البيانات عند أول عرض للصفحة
    _loadDataIfNeeded();

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const CustomAppBar(title: AppStrings.articles),
        body: Column(
          children: [
            // شريط البحث والفلترة
            _buildSearchAndFilter(),

            // قائمة المقالات
            Expanded(child: _buildArticlesList()),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.textLight.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            onChanged: _onSearchChanged,
            decoration: InputDecoration(
              hintText: 'البحث في المقالات...',
              prefixIcon: Icon(Icons.search, color: AppColors.textSecondary),
              suffixIcon:
                  _searchTerm.isNotEmpty
                      ? IconButton(
                        icon: Icon(Icons.clear, color: AppColors.textSecondary),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: AppColors.border),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: AppColors.primary),
              ),
              filled: true,
              fillColor: AppColors.surfaceVariant,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
            ),
          ),

          SizedBox(height: 12.h),

          // فلتر الفئات
          if (_categoriesWithCount.isNotEmpty) _buildCategoryFilter(),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return SizedBox(
      height: 40.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categoriesWithCount.length,
        itemBuilder: (context, index) {
          final category = _categoriesWithCount[index];
          final isSelected = _selectedCategoryId == category['id'];

          return _buildCategoryChip(
            id: category['id'],
            name: category['name'],
            count: category['count'],
            isSelected: isSelected,
          );
        },
      ),
    );
  }

  Widget _buildCategoryChip({
    required String? id,
    required String name,
    required int count,
    required bool isSelected,
  }) {
    return ArticleFilterButton(
      text: name,
      count: count,
      isSelected: isSelected,
      onTap: () => _onCategoryChanged(id),
    );
  }

  Widget _buildArticlesList() {
    if (_isLoading) {
      return Center(
        child: SizedBox(
          width: 60, // مقاس ثابت
          height: 60, // مقاس ثابت
          child: Stack(
            alignment: Alignment.center,
            children: [
              const CircularProgressIndicator(color: AppColors.primary),
              Container(
                width: 36, // مقاس ثابت
                height: 36, // مقاس ثابت
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 3,
                    ),
                  ],
                ),
                child: const CircleAvatar(
                  radius: 18, // مقاس ثابت
                  backgroundImage: AssetImage('assets/images/logo.jpeg'),
                  backgroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: AppColors.error),
            SizedBox(height: 16.h),
            Text(
              _errorMessage.isNotEmpty
                  ? _errorMessage
                  : 'حدث خطأ في تحميل المقالات',
              style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _refreshArticles,
              child: Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_articles.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _refreshArticles,
      color: AppColors.primary,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16.w),
        itemCount: _articles.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _articles.length) {
            return _buildLoadingMoreIndicator();
          }

          final article = _articles[index];
          return _buildArticleCard(article);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.article_outlined, size: 80.sp, color: AppColors.textLight),
          SizedBox(height: 16.h),
          Text(
            _searchTerm.isNotEmpty || _selectedCategoryId != null
                ? 'لا توجد مقالات تطابق البحث'
                : 'لا توجد مقالات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _searchTerm.isNotEmpty || _selectedCategoryId != null
                ? 'جرب تغيير معايير البحث'
                : 'سيتم إضافة المقالات قريباً',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textLight),
          ),
          if (_searchTerm.isNotEmpty || _selectedCategoryId != null) ...[
            SizedBox(height: 16.h),
            ClearFiltersButton(
              onPressed: () {
                _searchController.clear();
                _onSearchChanged('');
                _onCategoryChanged(null);
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Padding(
      padding: EdgeInsets.all(16.h),
      child: Center(
        child: SizedBox(
          width: 30, // مقاس ثابت
          height: 30, // مقاس ثابت
          child: Stack(
            alignment: Alignment.center,
            children: [
              const CircularProgressIndicator(
                color: AppColors.primary,
                strokeWidth: 2,
              ),
              const SizedBox(
                width: 18, // مقاس ثابت
                height: 18, // مقاس ثابت
                child: CircleAvatar(
                  radius: 9, // مقاس ثابت
                  backgroundImage: AssetImage('assets/images/logo.jpeg'),
                  backgroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildArticleCard(Article article) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: Stack(
          children: [
            // صورة الخلفية
            Container(
              height: 200.h,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.primary.withValues(alpha: 0.3),
                    AppColors.primary.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child:
                  article.imageUrl != null
                      ? Image.network(
                        article.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder:
                            (context, error, stackTrace) =>
                                _buildDefaultImage(),
                      )
                      : _buildDefaultImage(),
            ),

            // تدرج للنص
            Container(
              height: 200.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
            ),

            // المحتوى
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فئة المقال
                    if (article.hasCategory)
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          article.categoryName!,
                          style: TextStyle(
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColors.white,
                          ),
                        ),
                      ),

                    SizedBox(height: 8.h),

                    // العنوان
                    Text(
                      article.title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.white,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 8.h),

                    // الملخص
                    Text(
                      article.autoSummary,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.white.withValues(alpha: 0.9),
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 12.h),

                    // معلومات المقال
                    Row(
                      children: [
                        // الكاتب
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 4.h,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.secondary,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.person,
                                size: 12.sp,
                                color: AppColors.white,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                article.author,
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color: AppColors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),

                        Spacer(),

                        // التاريخ
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 4.h,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 12.sp,
                                color: AppColors.white,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                _formatDate(article.createdAt),
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color: AppColors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // زر القراءة
            Positioned.fill(
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _showArticleDetails(article),
                  borderRadius: BorderRadius.circular(16.r),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultImage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.6),
            AppColors.secondary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Center(
        child: Container(
          width: 80.w,
          height: 80.h,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: ClipOval(
            child: Image.asset(
              'assets/images/logo.jpeg',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.article,
                  size: 40.sp,
                  color: AppColors.white.withValues(alpha: 0.7),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';

    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()} أسابيع';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showArticleDetails(Article article) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ArticleDetailPage(article: article),
      ),
    );
  }
}
