import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../core/constants/app_colors.dart';

import '../../../core/utils/app_logger.dart';
import '../../bloc/medical_record/medical_record_bloc.dart';
import '../../bloc/medical_record/medical_record_event.dart';
import '../../bloc/medical_record/medical_record_state.dart';
import 'tabs/patient_info_tab.dart';
import 'tabs/weekly_results_tab.dart';
import 'tabs/medical_info_tab.dart';
import 'tabs/lab_tests_tab.dart';
import 'tabs/reminders_tab.dart';

class MedicalRecordPage extends StatefulWidget {
  final String? patientId;
  final String? authId;

  const MedicalRecordPage({super.key, this.patientId, this.authId});

  @override
  State<MedicalRecordPage> createState() => _MedicalRecordPageState();
}

class _MedicalRecordPageState extends State<MedicalRecordPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late MedicalRecordBloc _medicalRecordBloc;
  bool _hasLoadedData = false;

  final List<String> _tabTitles = [
    'المعلومات الشخصية',
    'النتائج الأسبوعية',
    'المعلومات الطبية',
    'الفحوصات المخبرية',
    'التذكيرات',
  ];

  final List<IconData> _tabIcons = [
    Icons.person,
    Icons.trending_up,
    Icons.medical_information,
    Icons.biotech,
    Icons.notifications,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _medicalRecordBloc = context.read<MedicalRecordBloc>();

    // لا نحمل البيانات عند التهيئة - سيتم التحميل عند الحاجة
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        _medicalRecordBloc.add(ChangeTab(_tabController.index));
      }
    });
  }

  void _loadDataIfNeeded() {
    if (!_hasLoadedData) {
      if (widget.patientId != null) {
        _medicalRecordBloc.add(LoadMedicalRecord(widget.patientId!));
      } else if (widget.authId != null) {
        _medicalRecordBloc.add(LoadMedicalRecordByAuthId(widget.authId!));
      }
      _hasLoadedData = true;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: BlocConsumer<MedicalRecordBloc, MedicalRecordState>(
          listener: (context, state) {
            if (state is MedicalRecordError) {
              AppLogger.error(
                'Medical record error: ${state.message}',
                category: LogCategory.ui,
              );
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: AppColors.error,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            }
          },
          builder: (context, state) {
            if (state is MedicalRecordLoading) {
              return _buildLoadingState();
            }

            if (state is MedicalRecordError) {
              return _buildErrorState(state.message);
            }

            if (state is MedicalRecordLoaded) {
              return _buildLoadedState(state);
            }

            // تحميل البيانات عند أول عرض للصفحة
            _loadDataIfNeeded();
            return _buildInitialState();
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'السجل الطبي',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppColors.primary),
            SizedBox(height: 16.h),
            Text(
              'جاري تحميل السجل الطبي...',
              style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'السجل الطبي',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80.sp, color: AppColors.error),
            SizedBox(height: 16.h),
            Text(
              message,
              style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: () {
                if (widget.patientId != null) {
                  _medicalRecordBloc.add(LoadMedicalRecord(widget.patientId!));
                } else if (widget.authId != null) {
                  _medicalRecordBloc.add(
                    LoadMedicalRecordByAuthId(widget.authId!),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                elevation: 2,
              ),
              icon: Icon(Icons.refresh, size: 18.sp),
              label: Text('إعادة المحاولة', style: TextStyle(fontSize: 16.sp)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialState() {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'السجل الطبي',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        centerTitle: true,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // محاولة إعادة تحميل البيانات
          final currentUser = Supabase.instance.client.auth.currentUser;
          if (currentUser != null) {
            _medicalRecordBloc.add(LoadMedicalRecordByAuthId(currentUser.id));
            await Future.delayed(const Duration(milliseconds: 500));
          }
        },
        color: AppColors.primary,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height - 200.h,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.medical_information_outlined,
                    size: 80.sp,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'لا توجد بيانات للعرض',
                    style: TextStyle(
                      fontSize: 18.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'اسحب لأسفل لإعادة المحاولة',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadedState(MedicalRecordLoaded state) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [_buildSliverAppBar(state), _buildSliverTabBar()];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildRefreshableTab(
              PatientInfoTab(patient: state.patient),
              state.patient?.id,
            ),
            _buildRefreshableTab(
              WeeklyResultsTab(
                weeklyResults: state.weeklyResults,
                patient: state.patient,
              ),
              state.patient?.id,
            ),
            _buildRefreshableTab(
              MedicalInfoTab(
                medicalInfo: state.medicalInfo,
                patientId: state.patient?.id,
              ),
              state.patient?.id,
            ),
            _buildRefreshableTab(
              LabTestsTab(
                labTests: state.labTests,
                patientId: state.patient?.id,
              ),
              state.patient?.id,
            ),
            _buildRefreshableTab(
              RemindersTab(
                reminders: state.reminders,
                patientId: state.patient?.id,
              ),
              state.patient?.id,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverAppBar(MedicalRecordLoaded state) {
    return SliverAppBar(
      expandedHeight: 160.h, // تقليل الارتفاع
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          state.patient?.displayName ?? 'السجل الطبي',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: _buildPatientSummary(state),
        ),
      ),
      // إزالة زر إعادة التحديث
    );
  }

  Widget _buildPatientSummary(MedicalRecordLoaded state) {
    final patient = state.patient;
    if (patient == null) return const SizedBox.shrink();

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 40.h), // Space for app bar
          Row(
            children: [
              CircleAvatar(
                radius: 25.r, // تصغير الصورة الرمزية
                backgroundColor: Colors.white.withValues(alpha: 0.2),
                child: Icon(Icons.person, size: 25.sp, color: Colors.white),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'ID: ${patient.id}',
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (patient.isPremium) ...[
                          SizedBox(width: 8.w),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6.w,
                              vertical: 2.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            child: Text(
                              'عضو مميز',
                              style: TextStyle(
                                fontSize: 9.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRefreshableTab(Widget child, String? patientId) {
    return RefreshIndicator(
      onRefresh: () async {
        if (patientId != null) {
          _medicalRecordBloc.add(RefreshMedicalRecord(patientId));

          // انتظار حتى اكتمال التحديث أو انتهاء المهلة الزمنية
          final completer = Completer<void>();
          late StreamSubscription subscription;

          subscription = _medicalRecordBloc.stream.listen((state) {
            if (state is MedicalRecordLoaded || state is MedicalRecordError) {
              if (!completer.isCompleted) {
                completer.complete();
                subscription.cancel();
              }
            }
          });

          // انتظار لمدة أقصاها 3 ثوان
          await Future.any([
            completer.future,
            Future.delayed(const Duration(seconds: 3)),
          ]);

          subscription.cancel();
        }
      },
      color: AppColors.primary,
      backgroundColor: Colors.white,
      strokeWidth: 2.5,
      displacement: 40.h,
      child: child,
    );
  }

  Widget _buildSliverTabBar() {
    return SliverPersistentHeader(
      delegate: _SliverTabBarDelegate(
        TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3.h,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
          unselectedLabelStyle: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.normal,
          ),
          tabs: List.generate(
            _tabTitles.length,
            (index) => Tab(
              icon: Icon(_tabIcons[index], size: 20.sp),
              text: _tabTitles[index],
            ),
          ),
        ),
      ),
      pinned: true,
    );
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverTabBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(color: AppColors.surface, child: _tabBar);
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
