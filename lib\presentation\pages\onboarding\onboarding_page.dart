import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:math' as math;
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/services/shared_preferences_service.dart';
import '../auth/login_page.dart';

/// صفحة التعريف بالتطبيق (Onboarding)
class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentPage = 0;
  final int _totalPages = 3;

  List<OnboardingData> get _pages => [
    OnboardingData(
      title: 'مرحباً بك في ${AppStrings.appName}',
      subtitle: 'نظام إدارة التغذية الذكي',
      description:
          'تطبيق شامل لإدارة صحتك الغذائية ومتابعة حالتك الصحية بطريقة ذكية ومتطورة',
      animationWidget: _buildHealthAnimation(),
      color: AppColors.primary,
    ),
    OnboardingData(
      title: 'تحليل الطعام الذكي',
      subtitle: 'اكتشف محتوى طعامك',
      description:
          'قم بتصوير وجبتك واحصل على تحليل مفصل للسعرات الحرارية والعناصر الغذائية فوراً',
      animationWidget: _buildFoodAnalysisAnimation(),
      color: AppColors.success,
    ),
    OnboardingData(
      title: 'متابعة طبية شاملة',
      subtitle: 'سجلك الطبي في مكان واحد',
      description:
          'احجز مواعيدك الطبية، تابع نتائج فحوصاتك، واحصل على استشارات غذائية متخصصة',
      animationWidget: _buildMedicalAnimation(),
      color: AppColors.warning,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _finishOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _finishOnboarding() async {
    final prefs = SharedPreferencesService();
    await prefs.setNotFirstTime();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) => const LoginPage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  // Animation widgets
  Widget _buildHealthAnimation() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(seconds: 2),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Container(
          width: 200.w,
          height: 200.h,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                AppColors.primary.withValues(alpha: 0.1),
                AppColors.primary.withValues(alpha: 0.05),
                Colors.transparent,
              ],
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Outer pulse ring
              AnimatedContainer(
                duration: Duration(milliseconds: (1000 * value).toInt()),
                width: 160.w * (0.8 + 0.2 * value),
                height: 160.h * (0.8 + 0.2 * value),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.primary.withValues(
                      alpha: 0.3 * (1 - value),
                    ),
                    width: 2.w,
                  ),
                ),
              ),
              // Middle pulse ring
              AnimatedContainer(
                duration: Duration(milliseconds: (800 * value).toInt()),
                width: 120.w * (0.9 + 0.1 * value),
                height: 120.h * (0.9 + 0.1 * value),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.primary.withValues(
                      alpha: 0.5 * (1 - value),
                    ),
                    width: 3.w,
                  ),
                ),
              ),
              // Center icon
              Container(
                width: 80.w,
                height: 80.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primary,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.health_and_safety_rounded,
                  size: 40.sp,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFoodAnalysisAnimation() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(seconds: 2),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Container(
          width: 200.w,
          height: 200.h,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Camera frame
              Container(
                width: 140.w,
                height: 140.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(color: AppColors.success, width: 3.w),
                  color: AppColors.success.withValues(alpha: 0.1),
                ),
                child: Icon(
                  Icons.camera_enhance_rounded,
                  size: 60.sp,
                  color: AppColors.success,
                ),
              ),
              // Scanning lines
              Positioned(
                top: 30.h + (80.h * value),
                child: Container(
                  width: 120.w,
                  height: 2.h,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        AppColors.success,
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              // Corner indicators
              ...List.generate(4, (index) {
                final positions = [
                  Alignment.topLeft,
                  Alignment.topRight,
                  Alignment.bottomLeft,
                  Alignment.bottomRight,
                ];
                return Positioned.fill(
                  child: Align(
                    alignment: positions[index],
                    child: AnimatedContainer(
                      duration: Duration(
                        milliseconds: (500 + index * 200).toInt(),
                      ),
                      width: 20.w * value,
                      height: 20.h * value,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.success,
                          width: 2.w,
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMedicalAnimation() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(seconds: 2),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Container(
          width: 200.w,
          height: 200.h,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Medical cross background
              Container(
                width: 120.w,
                height: 120.h,
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Icon(
                  Icons.medical_services_rounded,
                  size: 60.sp,
                  color: AppColors.warning,
                ),
              ),
              // Floating medical icons
              ...List.generate(6, (index) {
                final icons = [
                  Icons.favorite,
                  Icons.healing,
                  Icons.local_hospital,
                  Icons.medication,
                  Icons.monitor_heart,
                  Icons.vaccines,
                ];
                final angles = [0, 60, 120, 180, 240, 300];
                final radius = 80.w;
                final angle = (angles[index] + value * 360) * 3.14159 / 180;

                return Positioned(
                  left: 100.w + radius * math.cos(angle) - 15.w,
                  top: 100.h + radius * math.sin(angle) - 15.h,
                  child: AnimatedOpacity(
                    duration: Duration(milliseconds: 200 * index),
                    opacity: value,
                    child: Container(
                      width: 30.w,
                      height: 30.h,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.warning.withValues(alpha: 0.8),
                      ),
                      child: Icon(
                        icons[index],
                        size: 16.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (_currentPage > 0)
                    TextButton(
                      onPressed: _previousPage,
                      child: Text(
                        'السابق',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    )
                  else
                    const SizedBox(),
                  TextButton(
                    onPressed: _finishOnboarding,
                    child: Text(
                      'تخطي',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Page content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                  _animationController.reset();
                  _animationController.forward();
                },
                itemCount: _totalPages,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),

            // Bottom section
            Padding(
              padding: EdgeInsets.all(24.w),
              child: Column(
                children: [
                  // Page indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _totalPages,
                      (index) => _buildPageIndicator(index),
                    ),
                  ),

                  SizedBox(height: 32.h),

                  // Next/Get Started button
                  SizedBox(
                    width: double.infinity,
                    height: 56.h,
                    child: ElevatedButton(
                      onPressed: _nextPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _pages[_currentPage].color,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.r),
                        ),
                        elevation: 4,
                        padding: EdgeInsets.zero, // إزالة الـ padding الداخلي
                      ),
                      child: Text(
                        _currentPage == _totalPages - 1
                            ? 'ابدأ الآن'
                            : 'التالي',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 16.h),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(OnboardingData data) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Scrollbar(
              thumbVisibility: false, // إخفاء شريط التمرير إلا عند الحاجة
              child: SingleChildScrollView(
                // إضافة إمكانية التمرير مع تحسينات
                physics: const BouncingScrollPhysics(), // تمرير مرن
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight:
                        MediaQuery.of(context).size.height *
                        0.6, // حد أدنى للارتفاع
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Animation widget or icon
                      data.animationWidget ??
                          Container(
                            width: 120.w,
                            height: 120.h,
                            decoration: BoxDecoration(
                              color: data.color.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              data.icon ?? Icons.star,
                              size: 60.sp,
                              color: data.color,
                            ),
                          ),

                      SizedBox(height: 48.h),

                      // Title
                      Text(
                        data.title,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 28.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                          height: 1.2,
                        ),
                      ),

                      SizedBox(height: 16.h),

                      // Subtitle
                      Text(
                        data.subtitle,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                          color: data.color,
                        ),
                      ),

                      SizedBox(height: 24.h),

                      // Description
                      Text(
                        data.description,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textSecondary,
                          height: 1.5,
                        ),
                      ),

                      // مساحة إضافية للتأكد من إمكانية التمرير
                      SizedBox(height: 40.h),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPageIndicator(int index) {
    final isActive = index == _currentPage;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      width: isActive ? 24.w : 8.w,
      height: 8.h,
      decoration: BoxDecoration(
        color: isActive ? _pages[_currentPage].color : AppColors.border,
        borderRadius: BorderRadius.circular(4.r),
      ),
    );
  }
}

/// بيانات صفحة الـ Onboarding
class OnboardingData {
  final String title;
  final String subtitle;
  final String description;
  final IconData? icon;
  final Widget? animationWidget;
  final Color color;

  OnboardingData({
    required this.title,
    required this.subtitle,
    required this.description,
    this.icon,
    this.animationWidget,
    required this.color,
  });
}
