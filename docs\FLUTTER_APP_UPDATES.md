# تحديثات التطبيق Flutter للتوافق مع النظام الجديد

## نظرة عامة

بعد تطبيق النظام الجديد، التطبيق لم يعد بحاجة لإرسال الإشعارات المجدولة محلياً. سيقوم Supabase بهذه المهمة تلقائياً.

## التحديثات المطلوبة

### 1. إزالة الجدولة المحلية

يمكنك إزالة أو تعطيل الكود التالي من التطبيق:

```dart
// في NotificationService أو أي خدمة مشابهة
// لا حاجة لهذا الكود بعد الآن:

// ❌ إزالة هذا
// await _scheduleLocalNotifications();
// await _setupPeriodicNotificationCheck();
// Timer.periodic(Duration(minutes: 1), (timer) => checkScheduledNotifications());
```

### 2. تحديث خدمة الإشعارات

قم بتحديث `NotificationService` لإزالة الجدولة المحلية:

```dart
// lib/core/services/notification_service.dart

class NotificationService {
  // ... باقي الكود

  /// تهيئة الخدمة (محدثة)
  Future<void> initialize() async {
    try {
      AppLogger.info('🔔 Initializing Notification Service...');

      _prefs = SharedPreferencesService();
      _firebaseMessaging = FirebaseMessaging.instance;
      _localNotifications = FlutterLocalNotificationsPlugin();

      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // تهيئة Firebase Messaging
      await _initializeFirebaseMessaging();

      // طلب الأذونات
      await _requestPermissions();

      // إعداد معالجات الإشعارات
      _setupNotificationHandlers();

      // ❌ إزالة هذا السطر - لا حاجة للجدولة المحلية
      // await _setupScheduledNotifications();

      AppLogger.info('✅ Notification Service initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize notification service', error: e);
      rethrow;
    }
  }

  // ❌ يمكن إزالة هذه الدالة أو تعطيلها
  // Future<void> _setupScheduledNotifications() async {
  //   // لا حاجة لهذا بعد الآن
  // }

  // ✅ الاحتفاظ بهذه الدوال للإشعارات الفورية
  Future<void> showLocalNotification(NotificationModel notification) async {
    // هذه تبقى كما هي للإشعارات الفورية
  }

  Future<void> handleForegroundMessage(RemoteMessage message) async {
    // هذه تبقى كما هي
  }
}
```

### 3. تحديث إنشاء التذكيرات

عند إنشاء تذكير جديد، تأكد من إنشاء السجلات في Supabase:

```dart
// lib/core/services/reminder_service.dart

class ReminderService {
  
  /// إنشاء تذكير جديد
  Future<bool> createReminder(ReminderModel reminder) async {
    try {
      // 1. إنشاء التذكير في جدول reminders
      final reminderResult = await _supabase
          .from('reminders')
          .insert(reminder.toJson())
          .select()
          .single();

      // 2. إنشاء الإشعارات المجدولة في Supabase
      await _createScheduledNotifications(reminderResult['id'], reminder);

      AppLogger.info('✅ Reminder created successfully with server-side scheduling');
      return true;

    } catch (e) {
      AppLogger.error('Failed to create reminder', error: e);
      return false;
    }
  }

  /// إنشاء الإشعارات المجدولة في Supabase
  Future<void> _createScheduledNotifications(String reminderId, ReminderModel reminder) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return;

      // الحصول على بيانات المريض
      final patientData = await _supabase
          .from('patients')
          .select('id')
          .eq('auth_id', currentUser.id)
          .single();

      // إنشاء سجل في scheduled_notifications لكل يوم
      for (int dayOfWeek in reminder.daysOfWeek) {
        await _supabase.from('scheduled_notifications').insert({
          'patient_id': patientData['id'],
          'reminder_id': reminderId,
          'notification_type': reminder.type,
          'title': _getNotificationTitle(reminder.type),
          'body': reminder.description,
          'scheduled_time': reminder.time.format(context), // تحويل إلى HH:MM
          'days_of_week': [dayOfWeek],
          'is_active': true,
        });
      }

      AppLogger.info('📅 Scheduled notifications created in Supabase');

    } catch (e) {
      AppLogger.error('Failed to create scheduled notifications', error: e);
      rethrow;
    }
  }

  String _getNotificationTitle(String type) {
    switch (type) {
      case 'meal':
        return '🍽️ تذكير الوجبة';
      case 'exercise':
        return '🏃‍♂️ تذكير النشاط البدني';
      case 'medication':
        return '💊 تذكير الدواء';
      case 'water':
        return '💧 تذكير شرب الماء';
      default:
        return '🔔 تذكير من Diet Rx';
    }
  }
}
```

### 4. تحديث حذف/تعديل التذكيرات

```dart
/// حذف تذكير
Future<bool> deleteReminder(String reminderId) async {
  try {
    // 1. حذف الإشعارات المجدولة
    await _supabase
        .from('scheduled_notifications')
        .delete()
        .eq('reminder_id', reminderId);

    // 2. حذف التذكير
    await _supabase
        .from('reminders')
        .delete()
        .eq('id', reminderId);

    AppLogger.info('✅ Reminder and scheduled notifications deleted');
    return true;

  } catch (e) {
    AppLogger.error('Failed to delete reminder', error: e);
    return false;
  }
}

/// تعديل تذكير
Future<bool> updateReminder(ReminderModel reminder) async {
  try {
    // 1. تحديث التذكير
    await _supabase
        .from('reminders')
        .update(reminder.toJson())
        .eq('id', reminder.id);

    // 2. حذف الإشعارات المجدولة القديمة
    await _supabase
        .from('scheduled_notifications')
        .delete()
        .eq('reminder_id', reminder.id);

    // 3. إنشاء إشعارات مجدولة جديدة
    await _createScheduledNotifications(reminder.id, reminder);

    AppLogger.info('✅ Reminder updated with new scheduling');
    return true;

  } catch (e) {
    AppLogger.error('Failed to update reminder', error: e);
    return false;
  }
}
```

### 5. تحديث تفعيل/إلغاء التذكيرات

```dart
/// تفعيل أو إلغاء تذكير
Future<bool> toggleReminderStatus(String reminderId, bool isActive) async {
  try {
    // تحديث حالة التذكير
    await _supabase
        .from('reminders')
        .update({'is_active': isActive})
        .eq('id', reminderId);

    // تحديث حالة الإشعارات المجدولة
    await _supabase
        .from('scheduled_notifications')
        .update({'is_active': isActive})
        .eq('reminder_id', reminderId);

    AppLogger.info('✅ Reminder status updated: $isActive');
    return true;

  } catch (e) {
    AppLogger.error('Failed to toggle reminder status', error: e);
    return false;
  }
}
```

### 6. إضافة مراقبة حالة الإشعارات (اختياري)

يمكنك إضافة شاشة لمراقبة حالة الإشعارات المرسلة:

```dart
// lib/presentation/pages/notifications/notification_logs_page.dart

class NotificationLogsPage extends StatefulWidget {
  @override
  _NotificationLogsPageState createState() => _NotificationLogsPageState();
}

class _NotificationLogsPageState extends State<NotificationLogsPage> {
  List<Map<String, dynamic>> notificationLogs = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotificationLogs();
  }

  Future<void> _loadNotificationLogs() async {
    try {
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) return;

      // الحصول على بيانات المريض
      final patientData = await Supabase.instance.client
          .from('patients')
          .select('id')
          .eq('auth_id', currentUser.id)
          .single();

      // جلب سجلات الإشعارات
      final logs = await Supabase.instance.client
          .from('notification_logs')
          .select('*')
          .eq('patient_id', patientData['id'])
          .order('created_at', ascending: false)
          .limit(50);

      setState(() {
        notificationLogs = List<Map<String, dynamic>>.from(logs);
        isLoading = false;
      });

    } catch (e) {
      AppLogger.error('Failed to load notification logs', error: e);
      setState(() => isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('سجل الإشعارات')),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: notificationLogs.length,
              itemBuilder: (context, index) {
                final log = notificationLogs[index];
                return ListTile(
                  title: Text(log['title'] ?? ''),
                  subtitle: Text(log['body'] ?? ''),
                  trailing: _buildStatusIcon(log['status']),
                  onTap: () => _showLogDetails(log),
                );
              },
            ),
    );
  }

  Widget _buildStatusIcon(String? status) {
    switch (status) {
      case 'delivered':
        return Icon(Icons.check_circle, color: Colors.green);
      case 'failed':
        return Icon(Icons.error, color: Colors.red);
      case 'pending':
        return Icon(Icons.schedule, color: Colors.orange);
      default:
        return Icon(Icons.help, color: Colors.grey);
    }
  }

  void _showLogDetails(Map<String, dynamic> log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الإشعار'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('العنوان: ${log['title']}'),
            Text('المحتوى: ${log['body']}'),
            Text('الحالة: ${log['status']}'),
            Text('وقت الإنشاء: ${log['created_at']}'),
            if (log['sent_at'] != null)
              Text('وقت الإرسال: ${log['sent_at']}'),
            if (log['error_message'] != null)
              Text('رسالة الخطأ: ${log['error_message']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
```

## فوائد النظام الجديد

### 1. موثوقية أعلى
- الإشعارات تُرسل حتى لو كان التطبيق مغلقاً
- لا تعتمد على حالة الجهاز أو البطارية

### 2. أداء أفضل
- تقليل استهلاك البطارية في التطبيق
- تقليل العمليات في الخلفية

### 3. إدارة مركزية
- جميع الإشعارات تُدار من Supabase
- سهولة المراقبة والتشخيص

### 4. قابلية التوسع
- يدعم آلاف المستخدمين
- معالجة متوازية للإشعارات

## اختبار التحديثات

### 1. اختبار إنشاء تذكير:
```dart
// إنشاء تذكير جديد والتأكد من إنشاء scheduled_notifications
final reminder = ReminderModel(
  type: 'water',
  time: TimeOfDay(hour: 14, minute: 30),
  daysOfWeek: [1, 2, 3, 4, 5], // الاثنين إلى الجمعة
  description: 'اشرب كوب ماء',
  isActive: true,
);

await reminderService.createReminder(reminder);
```

### 2. اختبار حذف تذكير:
```dart
await reminderService.deleteReminder(reminderId);
```

### 3. فحص النتائج في Supabase:
```sql
-- فحص الإشعارات المجدولة
SELECT * FROM scheduled_notifications WHERE is_active = true;

-- فحص سجلات الإرسال
SELECT * FROM notification_logs ORDER BY created_at DESC LIMIT 10;
```

---

**🎉 تم! التطبيق الآن يعمل مع النظام الجديد**

الإشعارات ستُرسل تلقائياً من Supabase بدون الحاجة لأن يكون التطبيق مفتوحاً.
