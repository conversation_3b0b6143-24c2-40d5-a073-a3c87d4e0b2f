-- تحديث دالة إرسال الإشعارات لتستدعي Edge Function
CREATE OR REPLACE FUNCTION public.send_notifications_with_fcm()
RETURNS TABLE(notification_id uuid, patient_name text, status text, message text)
LANGUAGE plpgsql
AS $function$
DECLARE
    notification_record RECORD;
    patient_record RECORD;
    token_record RECORD;
    current_time_str TEXT;
    current_day_of_week INTEGER;
    notification_title TEXT;
    notification_body TEXT;
    tokens_count INTEGER := 0;
    log_id UUID;
BEGIN
    -- الحصول على الوقت الحالي بتوقيت الرياض (بدون ثوان)
    current_time_str := TO_CHAR((NOW() AT TIME ZONE 'Asia/Riyadh'), 'HH24:MI');
    current_day_of_week := EXTRACT(DOW FROM (NOW() AT TIME ZONE 'Asia/Riyadh'));
    
    -- تحويل الأحد من 0 إلى 7
    IF current_day_of_week = 0 THEN
        current_day_of_week := 7;
    END IF;
    
    RAISE NOTICE 'Current Riyadh time: %, Day: %', current_time_str, current_day_of_week;
    
    -- البحث عن الإشعارات المستحقة (مقارنة بدون ثوان)
    FOR notification_record IN
        SELECT *
        FROM scheduled_notifications
        WHERE is_active = true
        AND TO_CHAR(scheduled_time, 'HH24:MI') = current_time_str
        AND current_day_of_week = ANY(days_of_week)
    LOOP
        RAISE NOTICE 'Processing notification: % for time: %', notification_record.id, TO_CHAR(notification_record.scheduled_time, 'HH24:MI:SS');
        
        -- الحصول على بيانات المريض
        SELECT * INTO patient_record
        FROM patients
        WHERE id = notification_record.patient_id;
        
        IF patient_record.id IS NULL THEN
            notification_id := notification_record.id;
            patient_name := 'Unknown';
            status := 'failed';
            message := 'Patient not found';
            RETURN NEXT;
            CONTINUE;
        END IF;
        
        RAISE NOTICE 'Patient found: % (auth_id: %)', patient_record.name, patient_record.auth_id;
        
        -- تحضير عنوان ومحتوى الإشعار
        CASE notification_record.notification_type
            WHEN 'meal' THEN notification_title := '🍽️ تذكير الوجبة';
            WHEN 'exercise' THEN notification_title := '🏃‍♂️ تذكير النشاط البدني';
            WHEN 'medication' THEN notification_title := '💊 تذكير الدواء';
            WHEN 'water' THEN notification_title := '💧 تذكير شرب الماء';
            ELSE notification_title := '🔔 تذكير من Diet Rx';
        END CASE;
        
        notification_body := 'مرحباً ' || patient_record.name || '، ' || notification_record.body;
        
        -- عد FCM tokens
        SELECT COUNT(*) INTO tokens_count
        FROM user_fcm_tokens
        WHERE user_id = patient_record.auth_id
        AND is_active = true;
        
        RAISE NOTICE 'FCM tokens found: %', tokens_count;
        
        IF tokens_count = 0 THEN
            notification_id := notification_record.id;
            patient_name := patient_record.name;
            status := 'failed';
            message := 'No FCM tokens found';
            RETURN NEXT;
            CONTINUE;
        END IF;
        
        -- تسجيل الإشعار لكل token وإرساله عبر Edge Function
        FOR token_record IN
            SELECT fcm_token
            FROM user_fcm_tokens
            WHERE user_id = patient_record.auth_id
            AND is_active = true
        LOOP
            -- إنشاء سجل في notification_logs
            INSERT INTO notification_logs (
                scheduled_notification_id,
                patient_id,
                fcm_token,
                title,
                body,
                status
            ) VALUES (
                notification_record.id,
                notification_record.patient_id,
                token_record.fcm_token,
                notification_title,
                notification_body,
                'pending'
            ) RETURNING id INTO log_id;
            
            RAISE NOTICE 'Notification log created with ID: % for token: %...', log_id, LEFT(token_record.fcm_token, 20);
        END LOOP;
        
        notification_id := notification_record.id;
        patient_name := patient_record.name;
        status := 'queued';
        message := 'Notifications queued for ' || tokens_count || ' tokens';
        RETURN NEXT;
        
    END LOOP;
    
    -- إذا لم توجد إشعارات مستحقة
    IF NOT FOUND THEN
        notification_id := NULL;
        patient_name := 'None';
        status := 'info';
        message := 'No due notifications found for time: ' || current_time_str || ', day: ' || current_day_of_week;
        RETURN NEXT;
    END IF;
    
END;
$function$;

-- إنشاء دالة مجدولة تعمل كل دقيقة
CREATE OR REPLACE FUNCTION public.process_scheduled_notifications()
RETURNS void
LANGUAGE plpgsql
AS $function$
BEGIN
    -- استدعاء دالة إرسال الإشعارات
    PERFORM send_notifications_with_fcm();
    
    -- تسجيل وقت آخر تشغيل
    INSERT INTO notification_logs (
        scheduled_notification_id,
        patient_id,
        fcm_token,
        title,
        body,
        status,
        firebase_response
    ) VALUES (
        NULL,
        'system',
        'system',
        'System Check',
        'Scheduled notifications check completed at ' || NOW(),
        'system',
        ('{"timestamp": "' || NOW() || '", "type": "system_check"}')::jsonb
    );
END;
$function$;

-- إنشاء Cron Job لتشغيل الدالة كل دقيقة
-- ملاحظة: يجب تفعيل pg_cron extension أولاً
-- SELECT cron.schedule('process-notifications', '* * * * *', 'SELECT process_scheduled_notifications();');

-- إنشاء trigger لإرسال الإشعارات عند إدراج سجل جديد في notification_logs
CREATE OR REPLACE FUNCTION public.trigger_fcm_notification()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
    -- التحقق من أن السجل ليس من النظام وأن الحالة pending
    IF NEW.patient_id != 'system' AND NEW.status = 'pending' THEN
        -- هنا يمكن إضافة استدعاء Edge Function مباشرة
        -- أو الاعتماد على Database Webhook
        RAISE NOTICE 'New notification log created: %', NEW.id;
    END IF;
    
    RETURN NEW;
END;
$function$;

-- ربط Trigger بجدول notification_logs
DROP TRIGGER IF EXISTS on_notification_log_insert ON notification_logs;
CREATE TRIGGER on_notification_log_insert
    AFTER INSERT ON notification_logs
    FOR EACH ROW
    EXECUTE FUNCTION trigger_fcm_notification();

-- تحديث الدالة القديمة لتوجيه إلى الدالة الجديدة
CREATE OR REPLACE FUNCTION public.send_notifications_now()
RETURNS TABLE(notification_id uuid, patient_name text, status text, message text)
LANGUAGE plpgsql
AS $function$
BEGIN
    -- استدعاء الدالة الجديدة
    RETURN QUERY SELECT * FROM send_notifications_with_fcm();
END;
$function$;
