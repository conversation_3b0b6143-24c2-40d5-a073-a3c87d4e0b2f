import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/helpers.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_events.dart';
import '../../bloc/auth/auth_states.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_loading.dart';
import '../main/main_page.dart';
import 'register_page.dart';
import 'forgot_password_page.dart';

/// صفحة تسجيل الدخول
class LoginPage extends StatefulWidget {
  final String? initialEmail;
  final bool showSuccessMessage;
  final bool showPasswordResetSuccess;

  const LoginPage({
    super.key,
    this.initialEmail,
    this.showSuccessMessage = false,
    this.showPasswordResetSuccess = false,
  });

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();

    // تعيين البريد الإلكتروني المبدئي إذا تم تمريره
    if (widget.initialEmail != null) {
      _emailController.text = widget.initialEmail!;
    }

    // عرض رسالة النجاح بعد تأخير قصير
    if (widget.showSuccessMessage) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showAccountCreatedDialog();
      });
    } else if (widget.showPasswordResetSuccess) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showPasswordResetSuccessDialog();
      });
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _login() {
    if (_formKey.currentState!.validate()) {
      context.read<AuthBloc>().add(
        LoginRequested(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        ),
      );
    }
  }

  void _navigateToRegister() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const RegisterPage()));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthSuccess) {
              // الانتقال للصفحة الرئيسية مباشرة
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const MainPage()),
                (route) => false,
              );
            } else if (state is AuthFailure) {
              Helpers.showErrorSnackBar(context, state.message);
            } else if (state is ResetPasswordSent) {
              Helpers.showSuccessSnackBar(
                context,
                'تم إرسال رابط إعادة تعيين كلمة السر إلى ${state.email}',
              );
            }
          },
          builder: (context, state) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(24.w),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: 40.h),

                    // شعار التطبيق
                    _buildLogo(),

                    SizedBox(height: 24.h),

                    // عنوان الصفحة
                    _buildTitle(),

                    SizedBox(height: 32.h),

                    // حقل البريد الإلكتروني
                    FloatingLabelTextField(
                      controller: _emailController,
                      label: AppStrings.email,
                      hint: 'أدخل بريدك الإلكتروني',
                      keyboardType: TextInputType.emailAddress,
                      prefixIcon: Icons.email_outlined,
                      validator: Validators.validateEmail,
                      enabled: state is! AuthLoading,
                    ),

                    SizedBox(height: 20.h),

                    // حقل كلمة المرور
                    FloatingLabelTextField(
                      controller: _passwordController,
                      label: AppStrings.password,
                      hint: 'أدخل كلمة المرور',
                      obscureText: _obscurePassword,
                      prefixIcon: Icons.lock_outlined,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword
                              ? Icons.visibility_outlined
                              : Icons.visibility_off_outlined,
                          color: AppColors.textSecondary,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                      validator: Validators.validatePassword,
                      enabled: state is! AuthLoading,
                      onSubmitted: (_) => _login(),
                    ),

                    SizedBox(height: 12.h),

                    // نسيت كلمة المرور
                    _buildForgotPassword(),

                    SizedBox(height: 24.h),

                    // زر تسجيل الدخول
                    if (state is AuthLoading)
                      const CustomLoading(
                        message: 'جاري تسجيل الدخول...',
                        size: 40,
                      )
                    else
                      CustomButton(text: AppStrings.login, onPressed: _login),

                    SizedBox(height: 20.h),

                    // رابط إنشاء حساب
                    _buildRegisterLink(),

                    SizedBox(height: 24.h),

                    // Developed by
                    _buildDevelopedBy(),

                    SizedBox(height: 20.h),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Center(
      child: Container(
        height: 120, // مقاس ثابت
        width: 120, // مقاس ثابت
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: AppColors.primary, width: 4),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: const CircleAvatar(
          radius: 58, // مقاس ثابت
          backgroundImage: AssetImage('assets/images/logo.jpeg'),
          backgroundColor: AppColors.white,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'DIET RX',
          style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
            letterSpacing: 2.0,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.h),
        Text(
          'تسجيل الدخول',
          style: TextStyle(
            fontSize: 18.sp,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildForgotPassword() {
    return Align(
      alignment: Alignment.centerLeft,
      child: TextButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ForgotPasswordPage()),
          );
        },
        child: Text(
          AppStrings.forgotPassword,
          style: TextStyle(fontSize: 14.sp, color: AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildRegisterLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AppStrings.dontHaveAccount,
          style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
        ),
        TextButton(
          onPressed: _navigateToRegister,
          child: Text(
            AppStrings.createAccount,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDevelopedBy() {
    return Center(
      child: Text(
        'Developed and Powered by KhwassTech',
        style: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textLight,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  void _showAccountCreatedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Directionality(
            textDirection: TextDirection.rtl,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.r),
              ),
              title: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                    size: 28.sp,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'تم إنشاء الحساب بنجاح!',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تم إنشاء حساب جديد وتأكيده بنجاح.',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    'يمكنك الآن تسجيل الدخول باستخدام بريدك الإلكتروني وكلمة المرور.',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              actions: [
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    'حسناً',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _showPasswordResetSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Directionality(
            textDirection: TextDirection.rtl,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.r),
              ),
              title: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                    size: 28.sp,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'تم تغيير كلمة المرور!',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تم تغيير كلمة المرور بنجاح.',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    'يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              actions: [
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    'حسناً',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }
}
