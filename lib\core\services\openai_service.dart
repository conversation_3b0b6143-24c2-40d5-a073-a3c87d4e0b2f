import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import '../errors/exceptions.dart';
import '../utils/app_logger.dart';
import '../../domain/entities/food_analysis_result.dart';

/// خدمة OpenAI لتحليل الطعام
class OpenAIService {
  static const String _baseUrl = AppConstants.openAiBaseUrl;
  static const String _apiKey = AppConstants.openAiApiKey;
  static const String _model = AppConstants.openAiModel;

  /// تحليل الطعام من الصورة
  Future<FoodAnalysisResult> analyzeFood(File imageFile) async {
    AppLogger.info('🍎 Starting food analysis from image',
      category: LogCategory.api,
      data: {
        'imageSize': '${(await imageFile.length())} bytes',
        'imagePath': imageFile.path,
      }
    );

    try {
      // تحويل الصورة إلى base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      AppLogger.info('📷 Image converted to base64',
        category: LogCategory.api,
        data: {'base64Length': base64Image.length.toString()}
      );

      // إعداد الطلب
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': _model,
          'messages': [
            {
              'role': 'user',
              'content': [
                {
                  'type': 'text',
                  'text': '''
تحليل هذه الصورة وأعطني المعلومات التالية بالعربية في تنسيق JSON:

إذا كانت الصورة تحتوي على طعام:
{
  "food_name": "اسم الطعام",
  "calories": رقم السعرات الحرارية,
  "protein": رقم البروتين بالجرام,
  "carbs": رقم الكربوهيدرات بالجرام,
  "fats": رقم الدهون بالجرام,
  "saturated_fats": رقم الدهون المشبعة بالجرام,
  "unsaturated_fats": رقم الدهون غير المشبعة بالجرام,
  "sugars": رقم السكريات بالجرام,
  "added_sugars": رقم السكريات المضافة بالجرام,
  "fiber": رقم الألياف بالجرام,
  "sodium": رقم الصوديوم بالملليجرام,
  "serving_size": "حجم الحصة",
  "ingredients": ["قائمة المكونات"],
  "health_notes": "ملاحظات صحية مركزة على الحمية وإنقاص الوزن - اذكر إذا كان مناسب للحمية أم لا",
  "recommendations": "توصيات غذائية للأشخاص الذين يريدون إنقاص الوزن والحصول على جسم متناسق - قدم بدائل صحية إذا لزم الأمر"
}

إذا لم تكن الصورة تحتوي على طعام:
{
  "error": "نحن نحلل الطعام فقط. يرجى رفع صورة طعام للحصول على تحليل دقيق."
}

يرجى تقدير الكميات بناءً على حجم الحصة الظاهرة في الصورة.
                  '''
                },
                {
                  'type': 'image_url',
                  'image_url': {
                    'url': 'data:image/jpeg;base64,$base64Image'
                  }
                }
              ]
            }
          ],
          'max_tokens': 1000,
          'temperature': 0.3,
        }),
      );

      AppLogger.apiCall('POST', '$_baseUrl/chat/completions',
        statusCode: response.statusCode,
        duration: null
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        AppLogger.info('✅ OpenAI response received',
          category: LogCategory.api,
          data: {
            'hasChoices': (data['choices'] != null).toString(),
            'choicesCount': data['choices']?.length?.toString() ?? '0',
          }
        );

        final content = data['choices'][0]['message']['content'];

        // استخراج JSON من الاستجابة
        final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(content);
        if (jsonMatch != null) {
          final jsonString = jsonMatch.group(0)!;
          final analysisData = jsonDecode(jsonString);

          // التحقق من وجود رسالة خطأ (صورة ليست طعام)
          if (analysisData.containsKey('error')) {
            AppLogger.warning('⚠️ Non-food image detected',
              category: LogCategory.api,
              data: {'error': analysisData['error']}
            );

            throw const ValidationException(
              message: 'نحن نحلل الطعام فقط. يرجى رفع صورة طعام للحصول على تحليل دقيق.',
            );
          }

          AppLogger.info('✅ Food analysis completed successfully',
            category: LogCategory.api,
            data: {
              'foodName': analysisData['food_name']?.toString() ?? 'unknown',
              'calories': analysisData['calories']?.toString() ?? '0',
            }
          );

          return FoodAnalysisResult.fromJson(analysisData);
        } else {
          AppLogger.error('❌ Failed to parse OpenAI response',
            category: LogCategory.api,
            data: {'content': content}
          );

          throw const ParsingException(
            message: 'فشل في تحليل استجابة OpenAI',
          );
        }
      } else {
        AppLogger.error('❌ OpenAI API error',
          category: LogCategory.api,
          data: {
            'statusCode': response.statusCode.toString(),
            'responseBody': response.body,
          }
        );

        throw ServerException(
          message: 'فشل في تحليل الطعام',
          code: response.statusCode,
        );
      }
    } catch (e) {
      AppLogger.error('❌ Error in food analysis',
        category: LogCategory.api,
        data: {
          'errorType': e.runtimeType.toString(),
          'isAppException': (e is AppException).toString(),
        },
        error: e
      );

      if (e is AppException) rethrow;
      throw NetworkException(
        message: 'خطأ في الاتصال بخدمة تحليل الطعام: ${e.toString()}',
      );
    }
  }

  /// تحليل نص وصف الطعام
  Future<FoodAnalysisResult> analyzeFoodFromText(String foodDescription) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': _model,
          'messages': [
            {
              'role': 'user',
              'content': '''
تحليل هذا الوصف للطعام: "$foodDescription"
وأعطني المعلومات التالية بالعربية في تنسيق JSON:
{
  "food_name": "اسم الطعام",
  "calories": رقم السعرات الحرارية,
  "protein": رقم البروتين بالجرام,
  "carbs": رقم الكربوهيدرات بالجرام,
  "fats": رقم الدهون بالجرام,
  "sugars": رقم السكريات بالجرام,
  "fiber": رقم الألياف بالجرام,
  "sodium": رقم الصوديوم بالملليجرام,
  "serving_size": "حجم الحصة المعتاد",
  "ingredients": ["قائمة المكونات المحتملة"],
  "health_notes": "ملاحظات صحية",
  "recommendations": "توصيات غذائية"
}

يرجى تقدير الكميات بناءً على الحصة المعتادة.
              '''
            }
          ],
          'max_tokens': 1000,
          'temperature': 0.3,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];

        // استخراج JSON من الاستجابة
        final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(content);
        if (jsonMatch != null) {
          final jsonString = jsonMatch.group(0)!;
          final analysisData = jsonDecode(jsonString);

          return FoodAnalysisResult.fromJson(analysisData);
        } else {
          throw const ParsingException(
            message: 'فشل في تحليل استجابة OpenAI',
          );
        }
      } else {
        throw ServerException(
          message: 'فشل في تحليل الطعام',
          code: response.statusCode,
        );
      }
    } catch (e) {
      if (e is AppException) rethrow;
      throw NetworkException(
        message: 'خطأ في الاتصال بخدمة تحليل الطعام: ${e.toString()}',
      );
    }
  }
}
