import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/helpers.dart';
import '../../../domain/entities/food_analysis_result.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/custom_button.dart';

/// صفحة عرض نتائج تحليل الطعام
class FoodAnalysisResultPage extends StatefulWidget {
  final File foodImage;
  final FoodAnalysisResult analysisResult;

  const FoodAnalysisResultPage({
    super.key,
    required this.foodImage,
    required this.analysisResult,
  });

  @override
  State<FoodAnalysisResultPage> createState() => _FoodAnalysisResultPageState();
}

class _FoodAnalysisResultPageState extends State<FoodAnalysisResultPage>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutBack),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    // بدء الأنيميشن
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const CustomAppBar(
          title: 'نتائج التحليل',
          showBackButton: true,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // أنيميشن النجاح
                _buildSuccessAnimation(),

                SizedBox(height: 16.h),

                // صورة الطعام
                SlideTransition(
                  position: _slideAnimation,
                  child: _buildFoodImage(),
                ),

                SizedBox(height: 24.h),

                // معلومات الطعام
                SlideTransition(
                  position: _slideAnimation,
                  child: _buildFoodInfo(),
                ),

                SizedBox(height: 20.h),

                // المعلومات الغذائية
                SlideTransition(
                  position: _slideAnimation,
                  child: _buildNutritionInfo(),
                ),

                SizedBox(height: 20.h),

                // التقييم الصحي
                SlideTransition(
                  position: _slideAnimation,
                  child: _buildHealthRating(),
                ),

                SizedBox(height: 20.h),

                // الملاحظات والتوصيات
                if (widget.analysisResult.healthNotes.isNotEmpty ||
                    widget.analysisResult.recommendations.isNotEmpty)
                  SlideTransition(
                    position: _slideAnimation,
                    child: _buildNotesAndRecommendations(),
                  ),

                SizedBox(height: 32.h),

                // أزرار الإجراءات
                SlideTransition(
                  position: _slideAnimation,
                  child: _buildActionButtons(),
                ),

                SizedBox(height: 24.h),

                // التنويه القانوني
                SlideTransition(
                  position: _slideAnimation,
                  child: _buildLegalDisclaimer(),
                ),

                SizedBox(height: 20.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessAnimation() {
    return Center(
      child: SizedBox(
        width: 80.w,
        height: 80.h,
        child: Lottie.asset(
          'assets/animations/success.json',
          repeat: false,
          animate: true,
        ),
      ),
    );
  }

  Widget _buildFoodImage() {
    return Container(
      width: double.infinity,
      height: 250.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20.r),
        child: Stack(
          children: [
            // الصورة
            Image.file(
              widget.foodImage,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFoodInfo() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.restaurant,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات الطعام',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'تحليل مفصل للطعام المختار',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildInfoRow('اسم الطعام', widget.analysisResult.foodName),
          SizedBox(height: 12.h),
          _buildInfoRow('حجم الحصة', widget.analysisResult.servingSize),
        ],
      ),
    );
  }

  Widget _buildNutritionInfo() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.analytics,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                'المعلومات الغذائية',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildNutritionGrid(),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNutritionGrid() {
    final result = widget.analysisResult;

    // إنشاء قائمة بالعناصر الغذائية المتوفرة
    final nutritionItems = <Widget>[
      _buildAnimatedNutritionCard(
        'السعرات',
        '${result.calories.toInt()}',
        'سعرة',
        Icons.local_fire_department,
        Colors.orange,
      ),
      _buildAnimatedNutritionCard(
        'البروتين',
        '${result.protein.toInt()}',
        'جرام',
        Icons.fitness_center,
        Colors.red,
      ),
      _buildAnimatedNutritionCard(
        'الكربوهيدرات',
        '${result.carbs.toInt()}',
        'جرام',
        Icons.grain,
        Colors.amber,
      ),
      _buildAnimatedNutritionCard(
        'الدهون',
        '${result.fats.toInt()}',
        'جرام',
        Icons.opacity,
        Colors.blue,
      ),
    ];

    // إضافة الدهون المشبعة وغير المشبعة تحت الدهون
    nutritionItems.add(
      _buildAnimatedNutritionCard(
        'الدهون المشبعة',
        '${result.saturatedFats.toInt()}',
        'جرام',
        Icons.opacity,
        const Color(0xFF1565C0),
      ),
    );
    nutritionItems.add(
      _buildAnimatedNutritionCard(
        'الدهون غير المشبعة',
        '${result.unsaturatedFats.toInt()}',
        'جرام',
        Icons.opacity,
        const Color(0xFF42A5F5),
      ),
    );

    // إضافة جميع العناصر الغذائية بدون شروط
    nutritionItems.add(
      _buildAnimatedNutritionCard(
        'السكريات',
        '${result.sugars.toInt()}',
        'جرام',
        Icons.cake,
        Colors.pink,
      ),
    );

    // إضافة السكريات المضافة تحت السكريات
    nutritionItems.add(
      _buildAnimatedNutritionCard(
        'السكريات المضافة',
        '${result.addedSugars.toInt()}',
        'جرام',
        Icons.cake,
        const Color(0xFFE91E63),
      ),
    );

    nutritionItems.add(
      _buildAnimatedNutritionCard(
        'الألياف',
        '${result.fiber.toInt()}',
        'جرام',
        Icons.eco,
        Colors.green,
      ),
    );

    nutritionItems.add(
      _buildAnimatedNutritionCard(
        'الصوديوم',
        '${result.sodium.toInt()}',
        'ملجم',
        Icons.water_drop,
        Colors.cyan,
      ),
    );

    // عرض بطاقة واحدة في كل صف مع استغلال العرض الكامل
    return Column(
      children:
          nutritionItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;

            return Padding(
              padding: EdgeInsets.only(
                bottom:
                    index == nutritionItems.length - 1
                        ? 0
                        : 16.h, // تباعد أكبر بين البطاقات
              ),
              child: SizedBox(
                width: double.infinity, // استغلال العرض الكامل
                height: 80.h, // ارتفاع مناسب للمحتوى بدون أنيميشن
                child: item,
              ),
            );
          }).toList(),
    );
  }

  Widget _buildAnimatedNutritionCard(
    String label,
    String value,
    String unit,
    IconData icon,
    Color color,
  ) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, animationValue, child) {
        return Transform.scale(
          scale: 0.9 + (0.1 * animationValue),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: color.withValues(alpha: 0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.15),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                // اسم المعلومة الغذائية (الأول من اليمين)
                Expanded(
                  flex: 4,
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: 12.6.sp, // تقليل بنسبة 30% (18 * 0.7 = 12.6)
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.right,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                SizedBox(width: 16.w),

                // القيمة الرقمية (الثاني)
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        value,
                        style: TextStyle(
                          fontSize:
                              22.4.sp, // تقليل بنسبة 30% (32 * 0.7 = 22.4)
                          fontWeight: FontWeight.bold,
                          color: color,
                          height: 1.0,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 8.w),

                // الوحدة (الثالث)
                Expanded(
                  flex: 2,
                  child: Text(
                    unit,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: color.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.left,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHealthRating() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: _getHealthRatingColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getHealthRatingIcon(),
                  color: _getHealthRatingColor(),
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                'التقييم الصحي',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ...List.generate(5, (index) {
                    return Icon(
                      index < widget.analysisResult.healthRating
                          ? Icons.star
                          : Icons.star_border,
                      color: _getHealthRatingColor(),
                      size: 24.sp,
                    );
                  }),
                  SizedBox(width: 12.w),
                  Text(
                    widget.analysisResult.healthRatingText,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: _getHealthRatingColor(),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: _getHealthRatingColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: _getHealthRatingColor().withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  widget.analysisResult.dietAdvice,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: AppColors.textPrimary,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotesAndRecommendations() {
    return Column(
      children: [
        if (widget.analysisResult.healthNotes.isNotEmpty)
          _buildNotesSection(
            'ملاحظات صحية',
            widget.analysisResult.healthNotes,
            Icons.health_and_safety,
            Colors.blue,
          ),
        if (widget.analysisResult.healthNotes.isNotEmpty &&
            widget.analysisResult.recommendations.isNotEmpty)
          SizedBox(height: 16.h),
        if (widget.analysisResult.recommendations.isNotEmpty)
          _buildNotesSection(
            'توصيات',
            widget.analysisResult.recommendations,
            Icons.lightbulb,
            Colors.amber,
          ),
      ],
    );
  }

  Widget _buildNotesSection(
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(icon, color: color, size: 24.sp),
              ),
              SizedBox(width: 12.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Text(
            content,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimary,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        CustomButton(
          text: 'تحليل طعام آخر',
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: Icons.camera_enhance_rounded,
        ),
        SizedBox(height: 12.h),
        CustomButton(
          text: 'مشاركة النتائج',
          onPressed: _shareResults,
          backgroundColor: AppColors.surfaceVariant,
          textColor: AppColors.textPrimary,
          icon: Icons.share,
        ),
      ],
    );
  }

  Color _getHealthRatingColor() {
    final rating = widget.analysisResult.healthRating;
    if (rating >= 4) return Colors.green;
    if (rating >= 3) return Colors.orange;
    return Colors.red;
  }

  IconData _getHealthRatingIcon() {
    final rating = widget.analysisResult.healthRating;
    if (rating >= 4) return Icons.thumb_up;
    if (rating >= 3) return Icons.warning;
    return Icons.thumb_down;
  }

  Widget _buildLegalDisclaimer() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.info_outline_rounded,
            color: AppColors.warning,
            size: 20.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تنويه مهم',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.warning,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'هذه نتائج تقريبية وليست نتائج صحيحة 100%. يُنصح بمراجعة أخصائي تغذية للحصول على خطة غذائية دقيقة ومناسبة لحالتك الصحية.',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _shareResults() async {
    try {
      final result = widget.analysisResult;

      // إنشاء نص منسق للمشاركة
      final shareText = '''
🍎 تحليل الطعام - ${result.foodName}

📊 المعلومات الغذائية:
• السعرات الحرارية: ${result.calories.toInt()} سعرة
• البروتين: ${result.protein.toInt()} جرام
• الكربوهيدرات: ${result.carbs.toInt()} جرام
• الدهون: ${result.fats.toInt()} جرام
${result.fiber > 0 ? '• الألياف: ${result.fiber.toInt()} جرام\n' : ''}${result.sugars > 0 ? '• السكريات: ${result.sugars.toInt()} جرام\n' : ''}${result.sodium > 0 ? '• الصوديوم: ${result.sodium.toInt()} ملليجرام\n' : ''}
🥄 حجم الحصة: ${result.servingSize}

⭐ التقييم الصحي: ${result.healthRatingText} (${result.healthRating}/5)

${result.healthNotes.isNotEmpty ? '💡 ملاحظات صحية:\n${result.healthNotes}\n\n' : ''}${result.recommendations.isNotEmpty ? '📋 التوصيات:\n${result.recommendations}\n\n' : ''}
📱 تم تحليل الطعام باستخدام تطبيق DIET RX
      ''';

      // مشاركة النص
      await share_plus.Share.share(shareText);
    } catch (e) {
      if (mounted) {
        Helpers.showErrorSnackBar(context, 'فشل في مشاركة النتائج');
      }
    }
  }
}
