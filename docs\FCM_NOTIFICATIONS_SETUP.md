# إعداد إرسال الإشعارات المجدولة عبر Supabase و Firebase FCM v1

## نظرة عامة

هذا النظام يسمح بإرسال الإشعارات المجدولة من Supabase مباشرة إلى Firebase FCM v1 بدون الحاجة لأن يكون التطبيق مفتوحاً.

## المكونات

1. **Edge Function**: `send-fcm-notifications` - ترسل الإشعارات عبر Firebase FCM v1
2. **Database Functions**: دوال قاعدة البيانات للجدولة والمعالجة
3. **Database Webhook**: يربط بين قاعدة البيانات و Edge Function
4. **Cron Job**: لتشغيل الفحص الدوري للإشعارات المجدولة

## خطوات الإعداد

### 1. إعداد Firebase Service Account

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروعك
3. اذهب إلى **Project Settings** > **Service Accounts**
4. انقر على **Generate new private key**
5. احفظ الملف المُحمل (service-account.json)

### 2. إعداد Environment Variables في Supabase

1. اذهب إلى Supabase Dashboard
2. اذهب إلى **Settings** > **Edge Functions**
3. أضف المتغيرات التالية:

```bash
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----"
```

### 3. نشر Edge Function

```bash
# ربط المشروع المحلي بـ Supabase
supabase link --project-ref your-project-ref

# نشر Edge Function
supabase functions deploy send-fcm-notifications

# تعيين Environment Variables
supabase secrets set FIREBASE_PROJECT_ID=your-firebase-project-id
supabase secrets set FIREBASE_CLIENT_EMAIL=<EMAIL>
supabase secrets set FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----"
```

### 4. تطبيق Database Migration

```bash
# تطبيق التحديثات على قاعدة البيانات
supabase db push
```

### 5. إنشاء Database Webhook

1. اذهب إلى Supabase Dashboard
2. اذهب إلى **Database** > **Webhooks**
3. انقر على **Create a new hook**
4. املأ البيانات:
   - **Name**: FCM Notification Sender
   - **Table**: notification_logs
   - **Events**: Insert
   - **Type**: Edge Function
   - **Edge Function**: send-fcm-notifications
   - **HTTP method**: POST
   - **Timeout**: 10000ms

### 6. تفعيل Cron Job (اختياري)

إذا كنت تريد تشغيل الفحص تلقائياً كل دقيقة:

```sql
-- تفعيل pg_cron extension
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- إنشاء Cron Job
SELECT cron.schedule(
    'process-notifications', 
    '* * * * *', 
    'SELECT process_scheduled_notifications();'
);
```

## كيفية العمل

### 1. الجدولة
- دالة `send_notifications_with_fcm()` تعمل للبحث عن الإشعارات المستحقة
- تنشئ سجلات في جدول `notification_logs` بحالة `pending`

### 2. الإرسال
- عند إدراج سجل جديد في `notification_logs`، يتم تشغيل Webhook
- Webhook يستدعي Edge Function `send-fcm-notifications`
- Edge Function ترسل الإشعار عبر Firebase FCM v1
- تحديث حالة السجل إلى `delivered` أو `failed`

### 3. المراقبة
- جميع الإشعارات المرسلة تُسجل في `notification_logs`
- يمكن مراقبة حالة الإرسال والأخطاء
- Firebase response يُحفظ لكل إشعار

## الاختبار

### 1. اختبار يدوي
```sql
-- تشغيل فحص الإشعارات يدوياً
SELECT * FROM send_notifications_with_fcm();
```

### 2. اختبار Edge Function مباشرة
```bash
# اختبار Edge Function
curl -X POST 'https://your-project-ref.supabase.co/functions/v1/send-fcm-notifications' \
  -H 'Authorization: Bearer your-anon-key' \
  -H 'Content-Type: application/json' \
  -d '{
    "type": "INSERT",
    "table": "notification_logs",
    "record": {
      "id": "test-id",
      "fcm_token": "test-token",
      "title": "Test Notification",
      "body": "This is a test"
    }
  }'
```

## مراقبة الأخطاء

### 1. Logs في Supabase
- اذهب إلى **Edge Functions** > **send-fcm-notifications** > **Logs**

### 2. فحص notification_logs
```sql
-- فحص الإشعارات الفاشلة
SELECT * FROM notification_logs 
WHERE status = 'failed' 
ORDER BY created_at DESC;

-- فحص آخر الإشعارات
SELECT * FROM notification_logs 
ORDER BY created_at DESC 
LIMIT 10;
```

## الأمان

1. **Environment Variables**: جميع مفاتيح Firebase محفوظة كـ secrets في Supabase
2. **Service Account**: استخدام Service Account محدود الصلاحيات
3. **Webhook Security**: Webhook محمي بـ service key
4. **RLS**: Row Level Security مفعل على جميع الجداول

## استكشاف الأخطاء

### مشاكل شائعة:

1. **Firebase Authentication Error**
   - تأكد من صحة FIREBASE_PRIVATE_KEY
   - تأكد من تفعيل Firebase Cloud Messaging API

2. **Token Invalid**
   - تأكد من أن FCM tokens محدثة
   - تنظيف tokens غير النشطة

3. **Webhook لا يعمل**
   - تأكد من إعداد Webhook بشكل صحيح
   - فحص logs في Edge Function

4. **Cron Job لا يعمل**
   - تأكد من تفعيل pg_cron extension
   - فحص صلاحيات المستخدم
