import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:async';

import '../../../core/constants/app_colors.dart';
import '../../../core/utils/helpers.dart';
import '../../../core/services/email_verification_service.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_loading.dart';
import 'login_page.dart';

/// صفحة التحقق من البريد الإلكتروني
class EmailVerificationPage extends StatefulWidget {
  final String email;

  const EmailVerificationPage({super.key, required this.email});

  @override
  State<EmailVerificationPage> createState() => _EmailVerificationPageState();
}

class _EmailVerificationPageState extends State<EmailVerificationPage> {
  final _codeController = TextEditingController();
  bool _isLoading = false;
  bool _isResending = false;
  int _resendCountdown = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startResendCountdown();
  }

  @override
  void dispose() {
    _codeController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startResendCountdown() {
    setState(() {
      _resendCountdown = 60;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _verifyCode() async {
    if (_isLoading) return;

    final code = _codeController.text.trim();
    if (code.isEmpty) {
      Helpers.showErrorSnackBar(context, 'يرجى إدخال كود التحقق');
      return;
    }

    if (code.length != 6) {
      Helpers.showErrorSnackBar(context, 'كود التحقق يجب أن يكون 6 أرقام');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await EmailVerificationService.verifyCode(
        email: widget.email,
        token: code,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (result.isSuccess) {
          // عرض رسالة نجاح
          Helpers.showSuccessSnackBar(
            context,
            'تم إنشاء الحساب وتأكيده بنجاح! يمكنك الآن تسجيل الدخول',
          );

          // الانتقال لصفحة تسجيل الدخول
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder:
                  (context) => LoginPage(
                    initialEmail: widget.email,
                    showSuccessMessage: true,
                  ),
            ),
            (route) => false,
          );
        } else {
          Helpers.showErrorSnackBar(context, result.message);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        Helpers.showErrorSnackBar(context, 'حدث خطأ غير متوقع. حاول مرة أخرى.');
      }
    }
  }

  Future<void> _resendCode() async {
    if (_isResending || _resendCountdown > 0) return;

    setState(() {
      _isResending = true;
    });

    try {
      final result = await EmailVerificationService.resendVerificationCode(
        widget.email,
      );

      if (mounted) {
        setState(() {
          _isResending = false;
        });

        if (result.isSuccess) {
          Helpers.showSuccessSnackBar(context, result.message);
          _startResendCountdown();
        } else {
          Helpers.showErrorSnackBar(context, result.message);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
        Helpers.showErrorSnackBar(
          context,
          'حدث خطأ في إعادة الإرسال. حاول مرة أخرى.',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: 40.h),

              // أيقونة البريد الإلكتروني
              _buildEmailIcon(),

              SizedBox(height: 32.h),

              // العنوان
              _buildTitle(),

              SizedBox(height: 16.h),

              // الوصف
              _buildDescription(),

              SizedBox(height: 40.h),

              // حقل كود التحقق
              _buildCodeField(),

              SizedBox(height: 32.h),

              // زر التحقق
              if (_isLoading)
                const CustomLoading(message: 'جاري التحقق...', size: 40)
              else
                CustomButton(text: 'تحقق من الكود', onPressed: _verifyCode),

              SizedBox(height: 24.h),

              // زر إعادة الإرسال
              _buildResendButton(),

              SizedBox(height: 40.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmailIcon() {
    return Center(
      child: Container(
        width: 120.w,
        height: 120.h,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.primary.withValues(alpha: 0.1),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Icon(
          Icons.mark_email_read_rounded,
          size: 60.sp,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      'تحقق من بريدك الإلكتروني',
      style: TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription() {
    return Column(
      children: [
        Text(
          'لقد أرسلنا كود التحقق المكون من 6 أرقام إلى:',
          style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.h),
        Text(
          widget.email,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.primary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16.h),
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: AppColors.warning.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
          ),
          child: Text(
            'تحقق من صندوق الوارد وصندوق الرسائل المزعجة',
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColors.warning,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildCodeField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        color: AppColors.white,
      ),
      child: TextField(
        controller: _codeController,
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
        maxLength: 6,
        style: TextStyle(
          fontSize: 24.sp,
          fontWeight: FontWeight.bold,
          letterSpacing: 8.w,
          color: AppColors.textPrimary,
        ),
        decoration: InputDecoration(
          hintText: '000000',
          hintStyle: TextStyle(
            fontSize: 24.sp,
            color: AppColors.textLight,
            letterSpacing: 8.w,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 16.h),
          counterText: '',
        ),
        onSubmitted: (_) => _verifyCode(),
      ),
    );
  }

  Widget _buildResendButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'لم تستلم الكود؟ ',
          style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
        ),
        if (_isResending)
          SizedBox(
            width: 16.w,
            height: 16.h,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: AppColors.primary,
            ),
          )
        else if (_resendCountdown > 0)
          Text(
            'إعادة الإرسال بعد $_resendCountdown ثانية',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textLight),
          )
        else
          TextButton(
            onPressed: _resendCode,
            child: Text(
              'إعادة الإرسال',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }
}
