-- إصلاح جدول notification_logs وإضافة الأعمدة المفقودة

-- التحقق من وجود الجدول وإنشاؤه إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.notification_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    scheduled_notification_id UUID,
    patient_id TEXT NOT NULL,
    fcm_token TEXT NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    firebase_response JSONB,
    error_message TEXT
);

-- إضافة الأعمدة المفقودة إذا لم تكن موجودة
DO $$ 
BEGIN
    -- إضافة created_at إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notification_logs' 
                   AND column_name = 'created_at') THEN
        ALTER TABLE notification_logs ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
    
    -- إضافة sent_at إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notification_logs' 
                   AND column_name = 'sent_at') THEN
        ALTER TABLE notification_logs ADD COLUMN sent_at TIMESTAMP WITH TIME ZONE;
    END IF;
    
    -- إضافة error_message إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notification_logs' 
                   AND column_name = 'error_message') THEN
        ALTER TABLE notification_logs ADD COLUMN error_message TEXT;
    END IF;
END $$;

-- تحديث السجلات الموجودة لتعيين created_at إذا كان NULL
UPDATE notification_logs 
SET created_at = NOW() 
WHERE created_at IS NULL;

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_notification_logs_patient_id ON notification_logs(patient_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_status ON notification_logs(status);
CREATE INDEX IF NOT EXISTS idx_notification_logs_created_at ON notification_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_notification_logs_scheduled_notification_id ON notification_logs(scheduled_notification_id);

-- تفعيل Row Level Security
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان
DROP POLICY IF EXISTS "Users can view own notification logs" ON notification_logs;
CREATE POLICY "Users can view own notification logs" ON notification_logs
    FOR SELECT USING (
        patient_id = 'system' OR 
        patient_id IN (
            SELECT id FROM patients WHERE auth_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Service role can manage all notification logs" ON notification_logs;
CREATE POLICY "Service role can manage all notification logs" ON notification_logs
    FOR ALL USING (auth.role() = 'service_role');

-- إنشاء view لمراقبة أداء النظام (مُصحح)
CREATE OR REPLACE VIEW public.notification_system_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_notifications,
    COUNT(*) FILTER (WHERE status = 'delivered') as delivered_count,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_count,
    COUNT(*) FILTER (WHERE status = 'pending') as pending_count,
    ROUND(
        (COUNT(*) FILTER (WHERE status = 'delivered')::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 
        2
    ) as delivery_rate_percent
FROM notification_logs
WHERE patient_id != 'system'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- تعليق على الجدول
COMMENT ON TABLE notification_logs IS 'سجل جميع الإشعارات المرسلة عبر FCM';
COMMENT ON VIEW notification_system_stats IS 'إحصائيات أداء نظام الإشعارات اليومية';
