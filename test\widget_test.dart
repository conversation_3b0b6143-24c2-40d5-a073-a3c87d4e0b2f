// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:deit_rx/main.dart';

void main() {
  testWidgets('App should load login page initially', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const DietRxApp());

    // Wait for the app to initialize
    await tester.pumpAndSettle();

    // Verify that login page elements are present
    expect(find.text('مرحباً بك'), findsOneWidget);
    expect(find.text('تسجيل الدخول'), findsWidgets);
  });

  testWidgets('Login form validation test', (WidgetTester tester) async {
    await tester.pumpWidget(const DietRxApp());
    await tester.pumpAndSettle();

    // Find login button and tap it without entering credentials
    final loginButton = find.text('تسجيل الدخول').last;
    await tester.tap(loginButton);
    await tester.pump();

    // Should show validation errors
    expect(find.text('البريد الإلكتروني مطلوب'), findsOneWidget);
    expect(find.text('كلمة المرور مطلوبة'), findsOneWidget);
  });
}
