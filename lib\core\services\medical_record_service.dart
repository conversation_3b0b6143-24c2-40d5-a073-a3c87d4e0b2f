import 'package:supabase_flutter/supabase_flutter.dart';
import '../../data/models/patient_model.dart';
import '../../data/models/weekly_result_model.dart';
import '../../data/models/medical_info_model.dart';
import '../../data/models/lab_test_model.dart';
import '../../data/models/reminder_model.dart';
import '../utils/app_logger.dart';

class MedicalRecordService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  // Get patient information
  static Future<PatientModel?> getPatientInfo(String patientId) async {
    try {
      AppLogger.info(
        'Fetching patient info for ID: $patientId',
        category: LogCategory.api,
      );

      final response =
          await _supabase
              .from('patients')
              .select()
              .eq('id', patientId)
              .single();

      AppLogger.info(
        'Patient info fetched successfully',
        category: LogCategory.api,
      );
      return PatientModel.fromMap(response);
    } catch (e) {
      AppLogger.error(
        'Error fetching patient info',
        category: LogCategory.api,
        error: e,
      );
      return null;
    }
  }

  // Get patient by auth ID
  static Future<PatientModel?> getPatientByAuthId(String authId) async {
    try {
      AppLogger.info(
        'Fetching patient by auth ID: $authId',
        category: LogCategory.api,
      );

      final response =
          await _supabase
              .from('patients')
              .select()
              .eq('auth_id', authId)
              .single();

      AppLogger.info(
        'Patient fetched by auth ID successfully',
        category: LogCategory.api,
      );
      return PatientModel.fromMap(response);
    } catch (e) {
      AppLogger.error(
        'Error fetching patient by auth ID',
        category: LogCategory.api,
        error: e,
      );
      return null;
    }
  }

  // Get weekly results
  static Future<List<WeeklyResultModel>> getWeeklyResults(
    String patientId,
  ) async {
    try {
      AppLogger.info(
        'Fetching weekly results for patient: $patientId',
        category: LogCategory.api,
      );

      final response = await _supabase
          .from('weekly_results')
          .select()
          .eq('patient_id', patientId)
          .order('recorded_date', ascending: false);

      AppLogger.info(
        'Weekly results fetched successfully: ${response.length} records',
        category: LogCategory.api,
      );
      return response.map((item) => WeeklyResultModel.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching weekly results',
        category: LogCategory.api,
        error: e,
      );
      return [];
    }
  }

  // Get medical information
  static Future<List<MedicalInfoModel>> getMedicalInfo(String patientId) async {
    try {
      AppLogger.info(
        'Fetching medical info for patient: $patientId',
        category: LogCategory.api,
      );

      final response = await _supabase
          .from('medical_info')
          .select()
          .eq('patient_id', patientId)
          .order('created_at', ascending: false);

      AppLogger.info(
        'Medical info fetched successfully: ${response.length} records',
        category: LogCategory.api,
      );
      return response.map((item) => MedicalInfoModel.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching medical info',
        category: LogCategory.api,
        error: e,
      );
      return [];
    }
  }

  // Get medical information by type
  static Future<List<MedicalInfoModel>> getMedicalInfoByType(
    String patientId,
    String infoType,
  ) async {
    try {
      AppLogger.info(
        'Fetching medical info by type: $infoType for patient: $patientId',
        category: LogCategory.api,
      );

      final response = await _supabase
          .from('medical_info')
          .select()
          .eq('patient_id', patientId)
          .eq('info_type', infoType)
          .order('created_at', ascending: false);

      AppLogger.info(
        'Medical info by type fetched successfully: ${response.length} records',
        category: LogCategory.api,
      );
      return response.map((item) => MedicalInfoModel.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching medical info by type',
        category: LogCategory.api,
        error: e,
      );
      return [];
    }
  }

  // Get lab tests
  static Future<List<LabTestModel>> getLabTests(String patientId) async {
    try {
      AppLogger.info(
        'Fetching lab tests for patient: $patientId',
        category: LogCategory.api,
      );

      final response = await _supabase
          .from('lab_tests')
          .select()
          .eq('patient_id', patientId)
          .order('test_date', ascending: false);

      AppLogger.info(
        'Lab tests fetched successfully: ${response.length} records',
        category: LogCategory.api,
      );
      return response.map((item) => LabTestModel.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching lab tests',
        category: LogCategory.api,
        error: e,
      );
      return [];
    }
  }

  // Get reminders
  static Future<List<ReminderModel>> getReminders(String patientId) async {
    try {
      AppLogger.info(
        'Fetching reminders for patient: $patientId',
        category: LogCategory.api,
      );

      final response = await _supabase
          .from('reminders')
          .select()
          .eq('patient_id', patientId)
          .order('created_at', ascending: false);

      AppLogger.info(
        'Reminders fetched successfully: ${response.length} records',
        category: LogCategory.api,
      );
      return response.map((item) => ReminderModel.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching reminders',
        category: LogCategory.api,
        error: e,
      );
      return [];
    }
  }

  // Get active reminders
  static Future<List<ReminderModel>> getActiveReminders(
    String patientId,
  ) async {
    try {
      AppLogger.info(
        'Fetching active reminders for patient: $patientId',
        category: LogCategory.api,
      );

      final response = await _supabase
          .from('reminders')
          .select()
          .eq('patient_id', patientId)
          .eq('is_active', true)
          .order('reminder_time', ascending: true);

      AppLogger.info(
        'Active reminders fetched successfully: ${response.length} records',
        category: LogCategory.api,
      );
      return response.map((item) => ReminderModel.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching active reminders',
        category: LogCategory.api,
        error: e,
      );
      return [];
    }
  }

  // Get recent lab tests (last 6 months)
  static Future<List<LabTestModel>> getRecentLabTests(String patientId) async {
    try {
      final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));

      AppLogger.info(
        'Fetching recent lab tests for patient: $patientId',
        category: LogCategory.api,
      );

      final response = await _supabase
          .from('lab_tests')
          .select()
          .eq('patient_id', patientId)
          .gte('test_date', sixMonthsAgo.toIso8601String().split('T')[0])
          .order('test_date', ascending: false);

      AppLogger.info(
        'Recent lab tests fetched successfully: ${response.length} records',
        category: LogCategory.api,
      );
      return response.map((item) => LabTestModel.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching recent lab tests',
        category: LogCategory.api,
        error: e,
      );
      return [];
    }
  }

  // Get latest weekly result
  static Future<WeeklyResultModel?> getLatestWeeklyResult(
    String patientId,
  ) async {
    try {
      AppLogger.info(
        'Fetching latest weekly result for patient: $patientId',
        category: LogCategory.api,
      );

      final response = await _supabase
          .from('weekly_results')
          .select()
          .eq('patient_id', patientId)
          .order('recorded_date', ascending: false)
          .limit(1);

      if (response.isNotEmpty) {
        AppLogger.info(
          'Latest weekly result fetched successfully',
          category: LogCategory.api,
        );
        return WeeklyResultModel.fromMap(response.first);
      }

      AppLogger.info('No weekly results found', category: LogCategory.api);
      return null;
    } catch (e) {
      AppLogger.error(
        'Error fetching latest weekly result',
        category: LogCategory.api,
        error: e,
      );
      return null;
    }
  }

  // Get medical summary
  static Future<Map<String, dynamic>> getMedicalSummary(
    String patientId,
  ) async {
    try {
      AppLogger.info(
        'Fetching medical summary for patient: $patientId',
        category: LogCategory.api,
      );

      final patient = await getPatientInfo(patientId);
      final latestWeeklyResult = await getLatestWeeklyResult(patientId);
      final recentLabTests = await getRecentLabTests(patientId);
      final activeReminders = await getActiveReminders(patientId);
      final medicalInfo = await getMedicalInfo(patientId);

      final summary = {
        'patient': patient,
        'latest_weekly_result': latestWeeklyResult,
        'recent_lab_tests_count': recentLabTests.length,
        'active_reminders_count': activeReminders.length,
        'medical_conditions_count':
            medicalInfo.where((info) => info.infoType == 'condition').length,
        'medications_count':
            medicalInfo.where((info) => info.infoType == 'medication').length,
        'allergies_count':
            medicalInfo.where((info) => info.infoType == 'allergy').length,
      };

      AppLogger.info(
        'Medical summary fetched successfully',
        category: LogCategory.api,
      );
      return summary;
    } catch (e) {
      AppLogger.error(
        'Error fetching medical summary',
        category: LogCategory.api,
        error: e,
      );
      return {};
    }
  }
}
