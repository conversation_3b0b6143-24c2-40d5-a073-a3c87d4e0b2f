import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/helpers.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/services/email_verification_service.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_states.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_loading.dart';
import '../main/main_page.dart';
import 'login_page.dart';
import 'email_verification_page.dart';
import '../../../core/config/auth_config.dart';

/// صفحة إنشاء الحساب
class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  String? _selectedGender;
  DateTime? _selectedBirthDate;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    super.dispose();
  }

  Future<void> _register() async {
    if (_isLoading) return;

    AppLogger.info(
      '📝 Registration form submitted',
      category: LogCategory.ui,
      data: {
        'formValid': _formKey.currentState!.validate().toString(),
        'hasName': _nameController.text.trim().isNotEmpty.toString(),
        'hasEmail': _emailController.text.trim().isNotEmpty.toString(),
        'hasPassword': _passwordController.text.isNotEmpty.toString(),
      },
    );

    if (_formKey.currentState!.validate() &&
        _selectedGender != null &&
        _selectedBirthDate != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final result =
            AuthConfig.requireEmailVerification
                ? await EmailVerificationService.sendVerificationCode(
                  email: _emailController.text.trim(),
                  password: _passwordController.text,
                  fullName: _nameController.text.trim(),
                  phone: _phoneController.text.trim(),
                  birthDate: _selectedBirthDate!.toIso8601String(),
                  gender: _selectedGender!,
                  weight: _weightController.text.trim(),
                  height: _heightController.text.trim(),
                )
                : await EmailVerificationService.createAccountDirectly(
                  email: _emailController.text.trim(),
                  password: _passwordController.text,
                  fullName: _nameController.text.trim(),
                  phone: _phoneController.text.trim(),
                  birthDate: _selectedBirthDate!.toIso8601String(),
                  gender: _selectedGender!,
                  weight: _weightController.text.trim(),
                  height: _heightController.text.trim(),
                );

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          if (result.isSuccess) {
            if (AuthConfig.requireEmailVerification) {
              // الانتقال لصفحة التحقق
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => EmailVerificationPage(
                        email: _emailController.text.trim(),
                      ),
                ),
              );
            } else {
              // عرض رسالة نجاح والانتقال لصفحة تسجيل الدخول
              Helpers.showSuccessSnackBar(
                context,
                'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول',
              );

              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => LoginPage(
                        initialEmail: _emailController.text.trim(),
                        showSuccessMessage: true,
                      ),
                ),
                (route) => false,
              );
            }
          } else {
            Helpers.showErrorSnackBar(context, result.message);
          }
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          Helpers.showErrorSnackBar(
            context,
            'حدث خطأ غير متوقع. حاول مرة أخرى.',
          );
        }
      }
    } else {
      AppLogger.warning(
        '❌ Registration form validation failed',
        category: LogCategory.ui,
        data: {
          'nameEmpty': _nameController.text.trim().isEmpty.toString(),
          'emailEmpty': _emailController.text.trim().isEmpty.toString(),
          'passwordEmpty': _passwordController.text.isEmpty.toString(),
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthSuccess) {
              AppLogger.info(
                '✅ Registration successful',
                category: LogCategory.navigation,
                data: {
                  'patientId': state.patient.id,
                  'patientName': state.patient.name,
                  'patientEmail': state.patient.email ?? 'null',
                },
              );
              Helpers.showSuccessSnackBar(context, 'تم إنشاء الحساب بنجاح');
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const MainPage()),
                (route) => false,
              );
            } else if (state is AuthFailure) {
              AppLogger.error(
                '❌ Registration failed',
                category: LogCategory.ui,
                data: {
                  'errorMessage': state.message,
                  'errorCode': state.errorCode ?? 'unknown',
                },
              );
              Helpers.showErrorSnackBar(context, state.message);
            } else if (state is AuthLoading) {
              AppLogger.info(
                '⏳ Registration in progress',
                category: LogCategory.ui,
              );
            }
          },
          builder: (context, state) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(24.w),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: 20.h),

                    // شعار التطبيق
                    _buildLogo(),

                    SizedBox(height: 24.h),

                    // عنوان الصفحة
                    _buildTitle(),

                    SizedBox(height: 32.h),

                    // رسالة توضيحية
                    _buildInfoMessage(),

                    SizedBox(height: 24.h),

                    // حقل الاسم
                    FloatingLabelTextField(
                      controller: _nameController,
                      label: AppStrings.name,
                      hint: 'أدخل اسمك الكامل',
                      prefixIcon: Icons.person_outlined,
                      validator: Validators.validateName,
                      enabled: !_isLoading,
                    ),

                    SizedBox(height: 20.h),

                    // حقل البريد الإلكتروني
                    FloatingLabelTextField(
                      controller: _emailController,
                      label: AppStrings.email,
                      hint: 'أدخل بريدك الإلكتروني',
                      keyboardType: TextInputType.emailAddress,
                      prefixIcon: Icons.email_outlined,
                      validator: Validators.validateEmail,
                      enabled: !_isLoading,
                    ),

                    SizedBox(height: 20.h),

                    // حقل رقم الهاتف
                    FloatingLabelTextField(
                      controller: _phoneController,
                      label: 'رقم الهاتف',
                      hint: 'أدخل رقم هاتفك',
                      keyboardType: TextInputType.phone,
                      prefixIcon: Icons.phone_outlined,
                      validator: Validators.validatePhone,
                      enabled: !_isLoading,
                    ),

                    SizedBox(height: 20.h),

                    // حقل النوع
                    _buildGenderField(),

                    SizedBox(height: 20.h),

                    // حقل تاريخ الميلاد
                    _buildBirthDateField(),

                    SizedBox(height: 20.h),

                    // حقول الوزن والطول
                    Row(
                      children: [
                        Expanded(
                          child: FloatingLabelTextField(
                            controller: _weightController,
                            label: 'الوزن (كجم)',
                            hint: 'أدخل وزنك',
                            keyboardType: TextInputType.number,
                            prefixIcon: Icons.monitor_weight_outlined,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الوزن مطلوب';
                              }
                              final weight = double.tryParse(value);
                              if (weight == null ||
                                  weight < 30 ||
                                  weight > 300) {
                                return 'وزن غير صحيح (30-300 كجم)';
                              }
                              return null;
                            },
                            enabled: !_isLoading,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: FloatingLabelTextField(
                            controller: _heightController,
                            label: 'الطول (سم)',
                            hint: 'أدخل طولك',
                            keyboardType: TextInputType.number,
                            prefixIcon: Icons.height_outlined,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الطول مطلوب';
                              }
                              final height = double.tryParse(value);
                              if (height == null ||
                                  height < 100 ||
                                  height > 250) {
                                return 'طول غير صحيح (100-250 سم)';
                              }
                              return null;
                            },
                            enabled: !_isLoading,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 20.h),

                    // حقل كلمة المرور
                    FloatingLabelTextField(
                      controller: _passwordController,
                      label: AppStrings.password,
                      hint: 'أدخل كلمة مرور (8 خانات على الأقل)',
                      obscureText: _obscurePassword,
                      prefixIcon: Icons.lock_outlined,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword
                              ? Icons.visibility_outlined
                              : Icons.visibility_off_outlined,
                          color: AppColors.textSecondary,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                      validator: Validators.validatePassword,
                      enabled: !_isLoading,
                    ),

                    SizedBox(height: 20.h),

                    // حقل تأكيد كلمة المرور
                    FloatingLabelTextField(
                      controller: _confirmPasswordController,
                      label: AppStrings.confirmPassword,
                      hint: 'أعد إدخال كلمة المرور',
                      obscureText: _obscureConfirmPassword,
                      prefixIcon: Icons.lock_outlined,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscureConfirmPassword
                              ? Icons.visibility_outlined
                              : Icons.visibility_off_outlined,
                          color: AppColors.textSecondary,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureConfirmPassword = !_obscureConfirmPassword;
                          });
                        },
                      ),
                      validator:
                          (value) => Validators.validateConfirmPassword(
                            value,
                            _passwordController.text,
                          ),
                      enabled: !_isLoading,
                      onSubmitted: (_) => _register(),
                    ),

                    SizedBox(height: 32.h),

                    // زر إنشاء الحساب
                    if (_isLoading)
                      CustomLoading(
                        message: AuthConfig.loadingMessage,
                        size: 40,
                      )
                    else
                      CustomButton(
                        text: AuthConfig.registerButtonText,
                        onPressed: _register,
                      ),

                    SizedBox(height: 24.h),

                    // رابط تسجيل الدخول
                    _buildLoginLink(),

                    SizedBox(height: 40.h),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Center(
      child: Container(
        height: 100, // مقاس ثابت
        width: 100, // مقاس ثابت
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: AppColors.primary, width: 3),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: const CircleAvatar(
          radius: 48, // مقاس ثابت
          backgroundImage: AssetImage('assets/images/logo.jpeg'),
          backgroundColor: AppColors.white,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'DIET RX',
          style: TextStyle(
            fontSize: 28.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
            letterSpacing: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.h),
        Text(
          'إنشاء حساب جديد',
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInfoMessage() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.primary, size: 20.sp),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              'المعلومات التالية تقريبية وتساعدنا في تقديم تشخيص أفضل لحالتك الصحية',
              style: TextStyle(
                fontSize: 13.sp,
                color: AppColors.primary,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AppStrings.alreadyHaveAccount,
          style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            AppStrings.login,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGenderField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        color: AppColors.white,
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedGender,
        decoration: InputDecoration(
          labelText: 'النوع',
          prefixIcon: Icon(
            Icons.person_outline,
            color: AppColors.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 16.h,
          ),
        ),
        items: [
          DropdownMenuItem(value: 'male', child: Text('ذكر')),
          DropdownMenuItem(value: 'female', child: Text('أنثى')),
        ],
        onChanged:
            _isLoading
                ? null
                : (value) {
                  setState(() {
                    _selectedGender = value;
                  });
                },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى اختيار النوع';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildBirthDateField() {
    return FormField<DateTime>(
      validator: (value) {
        if (_selectedBirthDate == null) {
          return 'تاريخ الميلاد مطلوب';
        }
        final age =
            DateTime.now().difference(_selectedBirthDate!).inDays ~/ 365;
        if (age < 13) {
          return 'يجب أن يكون العمر 13 سنة على الأقل';
        }
        return null;
      },
      builder: (FormFieldState<DateTime> state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: state.hasError ? AppColors.error : AppColors.border,
                ),
                color: AppColors.white,
              ),
              child: InkWell(
                onTap:
                    _isLoading
                        ? null
                        : () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _selectedBirthDate ?? DateTime(2000),
                            firstDate: DateTime(1900),
                            lastDate: DateTime.now().subtract(
                              Duration(days: 365 * 13),
                            ), // 13 سنة على الأقل
                          );
                          if (date != null) {
                            setState(() {
                              _selectedBirthDate = date;
                            });
                            state.didChange(date);
                          }
                        },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 16.h,
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.cake_outlined, color: AppColors.textSecondary),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Text(
                          _selectedBirthDate != null
                              ? '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}'
                              : 'تاريخ الميلاد',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color:
                                _selectedBirthDate != null
                                    ? AppColors.textPrimary
                                    : AppColors.textSecondary,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.textSecondary,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (state.hasError)
              Padding(
                padding: EdgeInsets.only(top: 8.h, left: 16.w),
                child: Text(
                  state.errorText!,
                  style: TextStyle(color: AppColors.error, fontSize: 12.sp),
                ),
              ),
          ],
        );
      },
    );
  }
}
