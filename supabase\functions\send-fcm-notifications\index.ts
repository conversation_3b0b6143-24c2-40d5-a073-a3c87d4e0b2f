import { createClient } from 'npm:@supabase/supabase-js@2'
import { JWT } from 'npm:google-auth-library@9'

// Firebase Service Account Configuration
// يجب إضافة هذه المعلومات كـ Environment Variables في Supabase
const FIREBASE_CONFIG = {
  project_id: Deno.env.get('FIREBASE_PROJECT_ID')!,
  client_email: Deno.env.get('FIREBASE_CLIENT_EMAIL')!,
  private_key: Deno.env.get('FIREBASE_PRIVATE_KEY')!.replace(/\\n/g, '\n'),
}

interface NotificationLog {
  id: string
  scheduled_notification_id: string
  patient_id: string
  fcm_token: string
  title: string
  body: string
  status: string
}

interface WebhookPayload {
  type: 'INSERT'
  table: string
  record: NotificationLog
  schema: 'public'
}

const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
)

Deno.serve(async (req) => {
  try {
    console.log('🔔 FCM Notification Function Started')
    
    const payload: WebhookPayload = await req.json()
    console.log('📨 Received payload:', JSON.stringify(payload, null, 2))

    // التحقق من أن الطلب خاص بجدول notification_logs
    if (payload.table !== 'notification_logs') {
      return new Response(
        JSON.stringify({ error: 'Invalid table' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const notificationLog = payload.record

    // الحصول على Access Token من Firebase
    const accessToken = await getFirebaseAccessToken()
    console.log('🔑 Firebase Access Token obtained')

    // إرسال الإشعار عبر Firebase FCM v1
    const fcmResponse = await sendFCMNotification(
      accessToken,
      notificationLog.fcm_token,
      notificationLog.title,
      notificationLog.body
    )

    console.log('📤 FCM Response:', fcmResponse)

    // تحديث حالة الإشعار في قاعدة البيانات
    const updateResult = await updateNotificationStatus(
      notificationLog.id,
      fcmResponse.success ? 'delivered' : 'failed',
      fcmResponse
    )

    console.log('💾 Database update result:', updateResult)

    return new Response(
      JSON.stringify({
        success: fcmResponse.success,
        message: fcmResponse.success ? 'Notification sent successfully' : 'Failed to send notification',
        fcm_response: fcmResponse,
        notification_id: notificationLog.id
      }),
      {
        status: fcmResponse.success ? 200 : 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('❌ Error in FCM function:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        stack: error.stack
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
})

/**
 * الحصول على Access Token من Firebase باستخدام Service Account
 */
async function getFirebaseAccessToken(): Promise<string> {
  return new Promise((resolve, reject) => {
    const jwtClient = new JWT({
      email: FIREBASE_CONFIG.client_email,
      key: FIREBASE_CONFIG.private_key,
      scopes: ['https://www.googleapis.com/auth/firebase.messaging'],
    })

    jwtClient.authorize((err, tokens) => {
      if (err) {
        console.error('🔥 Firebase Auth Error:', err)
        reject(err)
        return
      }
      resolve(tokens!.access_token!)
    })
  })
}

/**
 * إرسال الإشعار عبر Firebase FCM v1 API
 */
async function sendFCMNotification(
  accessToken: string,
  fcmToken: string,
  title: string,
  body: string
): Promise<{ success: boolean; response?: any; error?: any }> {
  try {
    const fcmUrl = `https://fcm.googleapis.com/v1/projects/${FIREBASE_CONFIG.project_id}/messages:send`
    
    const message = {
      message: {
        token: fcmToken,
        notification: {
          title: title,
          body: body,
        },
        android: {
          notification: {
            icon: 'notilogo',
            color: '#2196F3',
            sound: 'default',
            priority: 'high',
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: title,
                body: body,
              },
              sound: 'default',
              badge: 1,
            },
          },
        },
        data: {
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
          type: 'scheduled_reminder',
        },
      },
    }

    console.log('📱 Sending FCM message:', JSON.stringify(message, null, 2))

    const response = await fetch(fcmUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify(message),
    })

    const responseData = await response.json()

    if (response.ok) {
      console.log('✅ FCM Success:', responseData)
      return { success: true, response: responseData }
    } else {
      console.error('❌ FCM Error:', responseData)
      return { success: false, error: responseData }
    }

  } catch (error) {
    console.error('💥 FCM Request Error:', error)
    return { success: false, error: error.message }
  }
}

/**
 * تحديث حالة الإشعار في قاعدة البيانات
 */
async function updateNotificationStatus(
  notificationLogId: string,
  status: string,
  fcmResponse: any
): Promise<any> {
  try {
    const { data, error } = await supabase
      .from('notification_logs')
      .update({
        status: status,
        firebase_response: fcmResponse,
        sent_at: new Date().toISOString(),
        error_message: fcmResponse.success ? null : JSON.stringify(fcmResponse.error)
      })
      .eq('id', notificationLogId)
      .select()

    if (error) {
      console.error('💾 Database update error:', error)
      return { success: false, error }
    }

    console.log('💾 Database updated successfully:', data)
    return { success: true, data }

  } catch (error) {
    console.error('💥 Database update exception:', error)
    return { success: false, error: error.message }
  }
}
