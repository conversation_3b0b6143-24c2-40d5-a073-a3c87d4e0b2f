# إعداد Database Webhook لإرسال الإشعارات

## نظرة عامة

Database Webhook يربط بين جدول `notification_logs` و Edge Function `send-fcm-notifications` لإرسال الإشعارات تلقائياً عند إدراج سجل جديد.

## خطوات الإعداد

### 1. الوصول إلى Webhook Settings

1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. اختر مشروعك
3. اذهب إلى **Database** > **Webhooks**
4. انقر على **Create a new hook**

### 2. إعداد Webhook

املأ النموذج بالبيانات التالية:

#### Basic Information
- **Name**: `FCM Notification Sender`
- **Description**: `Sends FCM notifications when new notification logs are created`

#### Trigger Conditions
- **Table**: `notification_logs`
- **Events**: ✅ **Insert** (فقط)
- **Schema**: `public`

#### Webhook Configuration
- **Type**: **Edge Function**
- **Edge Function**: `send-fcm-notifications`
- **HTTP method**: `POST`
- **Timeout**: `10000` (10 ثوان)

#### HTTP Headers
انقر على **Add new header** وأضف:
- **Content-Type**: `application/json`

ثم انقر على **Add auth header with service key** لإضافة header التفويض تلقائياً.

#### Filters (اختياري)
يمكنك إضافة فلتر لتجنب إرسال الإشعارات للسجلات النظامية:

```sql
NEW.patient_id != 'system' AND NEW.status = 'pending'
```

### 3. اختبار Webhook

بعد إنشاء Webhook، يمكنك اختباره:

#### اختبار من SQL Editor
```sql
-- إدراج إشعار تجريبي
INSERT INTO notification_logs (
    scheduled_notification_id,
    patient_id,
    fcm_token,
    title,
    body,
    status
) VALUES (
    gen_random_uuid(),
    'test-patient-id',
    'test-fcm-token',
    'اختبار الإشعار',
    'هذا إشعار تجريبي من Supabase',
    'pending'
);
```

#### فحص النتائج
```sql
-- فحص آخر الإشعارات المرسلة
SELECT 
    id,
    title,
    body,
    status,
    firebase_response,
    sent_at,
    error_message
FROM notification_logs 
ORDER BY created_at DESC 
LIMIT 5;
```

### 4. مراقبة Webhook

#### فحص Webhook Logs
1. اذهب إلى **Database** > **Webhooks**
2. انقر على webhook الخاص بك
3. اذهب إلى تبويب **Logs**

#### فحص Edge Function Logs
1. اذهب إلى **Edge Functions**
2. انقر على `send-fcm-notifications`
3. اذهب إلى تبويب **Logs**

### 5. استكشاف الأخطاء

#### مشاكل شائعة:

1. **Webhook لا يتم تشغيله**
   - تأكد من أن Events محدد على Insert
   - تأكد من أن Table محدد على notification_logs
   - فحص Filters إذا كانت موجودة

2. **Edge Function تفشل**
   - فحص Environment Variables
   - فحص Firebase Service Account
   - فحص Edge Function Logs

3. **FCM Token غير صحيح**
   - تأكد من أن FCM tokens محدثة في التطبيق
   - تنظيف tokens غير النشطة

#### أوامر مفيدة للتشخيص:

```sql
-- فحص الإشعارات الفاشلة
SELECT 
    title,
    body,
    status,
    error_message,
    firebase_response
FROM notification_logs 
WHERE status = 'failed'
ORDER BY created_at DESC;

-- فحص معدل نجاح الإرسال
SELECT 
    status,
    COUNT(*) as count,
    ROUND((COUNT(*)::DECIMAL / (SELECT COUNT(*) FROM notification_logs WHERE patient_id != 'system')) * 100, 2) as percentage
FROM notification_logs 
WHERE patient_id != 'system'
GROUP BY status;

-- فحص آخر 10 إشعارات
SELECT 
    created_at,
    title,
    LEFT(body, 50) as body_preview,
    status,
    CASE 
        WHEN sent_at IS NOT NULL THEN EXTRACT(EPOCH FROM (sent_at - created_at))::INTEGER
        ELSE NULL 
    END as processing_time_seconds
FROM notification_logs 
WHERE patient_id != 'system'
ORDER BY created_at DESC 
LIMIT 10;
```

### 6. تحسين الأداء

#### إعدادات Webhook المُحسنة:
- **Timeout**: 10000ms (للإشعارات الكثيفة)
- **Retry**: تفعيل إعادة المحاولة في حالة الفشل
- **Batch Size**: 1 (لضمان معالجة كل إشعار منفرداً)

#### مراقبة الأداء:
```sql
-- إحصائيات يومية
SELECT * FROM notification_system_stats 
ORDER BY date DESC 
LIMIT 7;

-- متوسط وقت المعالجة
SELECT 
    AVG(EXTRACT(EPOCH FROM (sent_at - created_at))) as avg_processing_seconds,
    MIN(EXTRACT(EPOCH FROM (sent_at - created_at))) as min_processing_seconds,
    MAX(EXTRACT(EPOCH FROM (sent_at - created_at))) as max_processing_seconds
FROM notification_logs 
WHERE sent_at IS NOT NULL 
AND patient_id != 'system'
AND created_at > NOW() - INTERVAL '24 hours';
```

## الأمان

### Headers مطلوبة:
- **Authorization**: Bearer token (يُضاف تلقائياً)
- **Content-Type**: application/json

### Validation:
- Webhook يتحقق من صحة البيانات الواردة
- Edge Function تتحقق من Firebase credentials
- جميع الأخطاء تُسجل للمراجعة

### Rate Limiting:
- Supabase تطبق rate limiting على Webhooks
- Edge Functions لها حدود استخدام شهرية
- مراقبة الاستخدام من Dashboard
