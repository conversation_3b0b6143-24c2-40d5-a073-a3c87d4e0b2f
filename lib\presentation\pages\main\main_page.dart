import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/app_logger.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_states.dart';
import '../appointments/appointments_page.dart';
import '../products/products_page.dart';
import '../articles/articles_page.dart';
import '../profile/profile_page.dart';
import '../medical_record/medical_record_page.dart';
import '../food_analysis/food_analysis_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;

/// الصفحة الرئيسية مع التنقل السفلي
class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  int _currentIndex = 0;
  final Map<int, Widget> _cachedPages = {}; // Cache للصفحات المحملة

  void _onTabTapped(int index) {
    final pageNames = [
      'Appointments',
      'Medical Record',
      'Food Analysis',
      'Articles',
      'Products',
      'Profile',
    ];
    final currentPageName =
        pageNames.length > _currentIndex ? pageNames[_currentIndex] : 'Unknown';
    final targetPageName =
        pageNames.length > index ? pageNames[index] : 'Unknown';

    AppLogger.navigation(currentPageName, targetPageName);

    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // تحديد القيم الافتراضية
        bool isPremium = false;
        String authId = '';

        // إذا كانت البيانات متاحة، استخدمها
        if (state is AuthSuccess) {
          final patient = state.patient;
          isPremium = patient.isPremium;
          authId = patient.authId ?? '';
        } else {
          // استخدام بيانات افتراضية أو من Supabase مباشرة
          final currentUser = Supabase.instance.client.auth.currentUser;
          authId = currentUser?.id ?? '';
          // افتراض أن المستخدم غير مميز حتى يتم تحميل البيانات
          isPremium = false;
        }

        return Scaffold(
          body: _buildCurrentPage(isPremium, authId),
          bottomNavigationBar: _buildBottomNavigationBar(isPremium),
        );
      },
    );
  }

  /// بناء الصفحة الحالية فقط (Lazy Loading)
  Widget _buildCurrentPage(bool isPremium, String authId) {
    // إذا كانت الصفحة محفوظة في Cache، استخدمها
    if (_cachedPages.containsKey(_currentIndex)) {
      return _cachedPages[_currentIndex]!;
    }

    // بناء الصفحة الجديدة وحفظها في Cache
    Widget page;

    if (isPremium) {
      // ترتيب الصفحات للمستخدمين المميزين
      switch (_currentIndex) {
        case 0:
          page = AppointmentsPage(authId: authId); // المواعيد
          break;
        case 1:
          page = _buildMedicalRecordPage(); // السجل الطبي
          break;
        case 2:
          page = const FoodAnalysisPage(); // تحليل الطعام
          break;
        case 3:
          page = const ArticlesPage(); // المقالات
          break;
        case 4:
          page = const ProductsPage(); // المنتجات
          break;
        case 5:
          page = const ProfilePage(); // الملف الشخصي
          break;
        default:
          page = AppointmentsPage(authId: authId);
      }
    } else {
      // ترتيب الصفحات للمستخدمين العاديين
      switch (_currentIndex) {
        case 0:
          page = AppointmentsPage(authId: authId); // المواعيد
          break;
        case 1:
          page = const ArticlesPage(); // المقالات
          break;
        case 2:
          page = const ProductsPage(); // المنتجات
          break;
        case 3:
          page = const ProfilePage(); // الملف الشخصي
          break;
        default:
          page = AppointmentsPage(authId: authId);
      }
    }

    // حفظ الصفحة في Cache
    _cachedPages[_currentIndex] = page;
    return page;
  }

  Widget _buildMedicalRecordPage() {
    // الحصول على معرف المستخدم الحالي
    final currentUser = Supabase.instance.client.auth.currentUser;

    if (currentUser != null) {
      return MedicalRecordPage(authId: currentUser.id);
    } else {
      // في حالة عدم وجود مستخدم، عرض صفحة فارغة
      return const MedicalRecordPage();
    }
  }

  Widget _buildBottomNavigationBar(bool isPremium) {
    final items = <BottomNavigationBarItem>[
      const BottomNavigationBarItem(
        icon: Icon(Icons.event_note_rounded), // أيقونة حديثة للمواعيد
        label: AppStrings.appointments,
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.library_books_rounded), // أيقونة حديثة للمقالات
        label: AppStrings.articles,
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.storefront_rounded), // أيقونة حديثة للمنتجات
        label: AppStrings.products,
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.account_circle_rounded), // أيقونة حديثة للملف الشخصي
        label: AppStrings.profile,
      ),
    ];

    if (isPremium) {
      // إضافة السجل الطبي للمستخدمين المميزين بعد المواعيد
      items.insert(
        1,
        const BottomNavigationBarItem(
          icon: Icon(
            Icons.health_and_safety_rounded,
          ), // أيقونة حديثة للسجل الطبي
          label: AppStrings.medicalRecord,
        ),
      );
      // إضافة تحليل الطعام للمستخدمين المميزين
      items.insert(
        2,
        const BottomNavigationBarItem(
          icon: Icon(
            Icons.camera_enhance_rounded,
          ), // أيقونة حديثة لتحليل الطعام
          label: AppStrings.foodAnalysis,
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColors.white,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textLight,
        selectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12.sp,
          fontWeight: FontWeight.w400,
        ),
        items: items,
      ),
    );
  }
}

/// صفحة رئيسية مبسطة للمستخدمين العاديين
class SimpleMainPage extends StatefulWidget {
  const SimpleMainPage({super.key});

  @override
  State<SimpleMainPage> createState() => _SimpleMainPageState();
}

class _SimpleMainPageState extends State<SimpleMainPage> {
  int _currentIndex = 0;
  final Map<int, Widget> _cachedPages = {}; // Cache للصفحات المحملة

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  /// بناء الصفحة الحالية فقط (Lazy Loading)
  Widget _buildCurrentPage() {
    // الحصول على معرف المصادقة من المستخدم الحالي
    final currentUser = Supabase.instance.client.auth.currentUser;
    final authId = currentUser?.id ?? '';

    // إذا كانت الصفحة محفوظة في Cache، استخدمها
    if (_cachedPages.containsKey(_currentIndex)) {
      return _cachedPages[_currentIndex]!;
    }

    // بناء الصفحة الجديدة وحفظها في Cache
    Widget page;
    switch (_currentIndex) {
      case 0:
        page = AppointmentsPage(authId: authId); // المواعيد
        break;
      case 1:
        page = const ArticlesPage(); // المقالات
        break;
      case 2:
        page = const ProductsPage(); // المنتجات
        break;
      case 3:
        page = const ProfilePage(); // الملف الشخصي
        break;
      default:
        page = AppointmentsPage(authId: authId);
    }

    // حفظ الصفحة في Cache
    _cachedPages[_currentIndex] = page;
    return page;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildCurrentPage(),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor: AppColors.white,
          selectedItemColor: AppColors.primary,
          unselectedItemColor: AppColors.textLight,
          selectedLabelStyle: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.event_note_rounded), // أيقونة حديثة للمواعيد
              label: AppStrings.appointments,
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.library_books_rounded), // أيقونة حديثة للمقالات
              label: AppStrings.articles,
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.storefront_rounded), // أيقونة حديثة للمنتجات
              label: AppStrings.products,
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.account_circle_rounded,
              ), // أيقونة حديثة للملف الشخصي
              label: AppStrings.profile,
            ),
          ],
        ),
      ),
    );
  }
}
