-- إ<PERSON><PERSON><PERSON> Cron Job لتشغيل فحص الإشعارات المجدولة تلقائياً

-- تفعيل pg_cron extension
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- حذف أي Cron Job موجود مسبقاً
SELECT cron.unschedule('process-notifications');

-- إنشاء Cron Job جديد يعمل كل دقيقة
SELECT cron.schedule(
    'process-notifications',           -- اسم المهمة
    '* * * * *',                      -- كل دقيقة
    'SELECT process_scheduled_notifications();'  -- الدالة المراد تشغيلها
);

-- إنشاء جدول لتتبع تشغيل Cron Jobs (اختياري)
CREATE TABLE IF NOT EXISTS public.cron_job_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_name TEXT NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'running',
    error_message TEXT,
    notifications_processed INTEGER DEFAULT 0
);

-- دالة محسنة لمعالجة الإشعارات مع تسجيل الأداء
CREATE OR REPLACE FUNCTION public.process_scheduled_notifications_with_logging()
RETURNS void
LANGUAGE plpgsql
AS $function$
DECLARE
    log_id UUID;
    notification_count INTEGER := 0;
    error_msg TEXT;
BEGIN
    -- إنشاء سجل بداية التشغيل
    INSERT INTO cron_job_logs (job_name, status)
    VALUES ('process-notifications', 'running')
    RETURNING id INTO log_id;
    
    BEGIN
        -- تشغيل معالجة الإشعارات
        SELECT COUNT(*) INTO notification_count
        FROM send_notifications_with_fcm();
        
        -- تحديث سجل النجاح
        UPDATE cron_job_logs
        SET 
            completed_at = NOW(),
            status = 'completed',
            notifications_processed = notification_count
        WHERE id = log_id;
        
        -- تسجيل في الـ logs
        RAISE NOTICE 'Cron job completed successfully. Processed % notifications.', notification_count;
        
    EXCEPTION WHEN OTHERS THEN
        -- تسجيل الخطأ
        GET STACKED DIAGNOSTICS error_msg = MESSAGE_TEXT;
        
        UPDATE cron_job_logs
        SET 
            completed_at = NOW(),
            status = 'failed',
            error_message = error_msg
        WHERE id = log_id;
        
        RAISE WARNING 'Cron job failed: %', error_msg;
    END;
END;
$function$;

-- تحديث Cron Job لاستخدام الدالة المحسنة
SELECT cron.unschedule('process-notifications');
SELECT cron.schedule(
    'process-notifications',
    '* * * * *',
    'SELECT process_scheduled_notifications_with_logging();'
);

-- دالة لتنظيف سجلات Cron Job القديمة (تحتفظ بآخر 1000 سجل)
CREATE OR REPLACE FUNCTION public.cleanup_cron_logs()
RETURNS void
LANGUAGE plpgsql
AS $function$
BEGIN
    DELETE FROM cron_job_logs
    WHERE id NOT IN (
        SELECT id FROM cron_job_logs
        ORDER BY started_at DESC
        LIMIT 1000
    );
    
    RAISE NOTICE 'Cron logs cleanup completed';
END;
$function$;

-- جدولة تنظيف السجلات يومياً في الساعة 2:00 صباحاً
SELECT cron.schedule(
    'cleanup-cron-logs',
    '0 2 * * *',
    'SELECT cleanup_cron_logs();'
);

-- عرض معلومات Cron Jobs المجدولة
SELECT 
    jobname,
    schedule,
    command,
    active
FROM cron.job
WHERE jobname IN ('process-notifications', 'cleanup-cron-logs');

-- دالة للتحقق من حالة Cron Jobs
CREATE OR REPLACE FUNCTION public.get_cron_job_status()
RETURNS TABLE(
    job_name TEXT,
    last_run TIMESTAMP WITH TIME ZONE,
    status TEXT,
    notifications_processed INTEGER,
    error_message TEXT
)
LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        cjl.job_name,
        cjl.completed_at as last_run,
        cjl.status,
        cjl.notifications_processed,
        cjl.error_message
    FROM cron_job_logs cjl
    WHERE cjl.id IN (
        SELECT DISTINCT ON (job_name) id
        FROM cron_job_logs
        ORDER BY job_name, started_at DESC
    )
    ORDER BY cjl.started_at DESC;
END;
$function$;

-- إنشاء view لمراقبة أداء النظام
CREATE OR REPLACE VIEW public.notification_system_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_notifications,
    COUNT(*) FILTER (WHERE status = 'delivered') as delivered_count,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_count,
    COUNT(*) FILTER (WHERE status = 'pending') as pending_count,
    ROUND(
        (COUNT(*) FILTER (WHERE status = 'delivered')::DECIMAL / COUNT(*)) * 100, 
        2
    ) as delivery_rate_percent
FROM notification_logs
WHERE patient_id != 'system'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- تعليقات للمساعدة
COMMENT ON FUNCTION process_scheduled_notifications_with_logging() IS 'دالة معالجة الإشعارات المجدولة مع تسجيل الأداء';
COMMENT ON FUNCTION cleanup_cron_logs() IS 'دالة تنظيف سجلات Cron Job القديمة';
COMMENT ON FUNCTION get_cron_job_status() IS 'دالة للتحقق من حالة آخر تشغيل لـ Cron Jobs';
COMMENT ON VIEW notification_system_stats IS 'إحصائيات أداء نظام الإشعارات اليومية';
